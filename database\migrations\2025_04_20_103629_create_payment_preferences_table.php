<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('preferred_method', ['credit_card', 'bank_transfer', 'cash_on_delivery', 'check']);
            $table->string('payment_terms')->nullable()->comment('Net 30, Immediate, etc.');
            $table->boolean('save_for_future')->default(false);
            $table->json('metadata')->nullable()->comment('Additional payment details');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        DB::statement('CREATE INDEX idx_payment_prefs_user ON payment_preferences (user_id)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_preferences');
    }
};
