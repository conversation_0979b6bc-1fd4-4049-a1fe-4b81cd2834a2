@extends('backend.layouts.master')

@section('title', 'Create Visit Reminder')

@section('main-content')
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Create Visit Reminder</h6>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('salesman.visit.store') }}">
            @csrf

            <div class="form-group">
                <label for="customer_id">Customer</label>
                <select name="customer_id" id="customer_id" class="form-control" required>
                    <option value="">Select Customer</option>
                    @foreach($customers as $customer)
                        <option value="{{ $customer->id }}">{{ $customer->first_name }} {{ $customer->last_name }}</option>
                    @endforeach
                </select>
                @error('customer_id')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="visit_date">Visit Date</label>
                <input type="datetime-local" name="visit_date" id="visit_date" value="{{ old('visit_date') }}" class="form-control" required>
                @error('visit_date')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="note">Note (Optional)</label>
                <textarea name="note" id="note" rows="4" class="form-control">{{ old('note') }}</textarea>
                @error('note')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="inline-flex items-center ml-4">
                    <input type="checkbox" name="is_recurring" id="is_recurring" value="1" class="form-check-input" {{ old('is_recurring') ? 'checked' : '' }}>
                    <span class="ml-2">Is Recurring?</span>
                </label>
                @error('is_recurring')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="recurring_options d-none">
                <div class="form-group">
                    <label for="interval">Interval</label>
                    <input type="number" name="interval" id="interval" value="{{ old('interval') }}" class="form-control">
                    @error('interval')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="frequency">Frequency</label>
                    <select name="frequency" id="frequency" class="form-control">
                        <option value="">Select Frequency</option>
                        <option value="day">Day</option>
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                        <option value="year">Year</option>
                    </select>
                    @error('frequency')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Create Reminder</button>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#is_recurring').change(function() {
            if ($(this).is(':checked')) {
                $('.recurring_options').removeClass('d-none');
            } else {
                $('.recurring_options').addClass('d-none');
            }
        });
    });
</script>
@endpush
