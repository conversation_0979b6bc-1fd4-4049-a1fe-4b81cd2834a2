@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Upload Customers</h5>
    <div class="card-body">
        <form method="post" action="{{ route('import.customers') }}" enctype="multipart/form-data">
            @csrf

            <div class="form-group">
                <label for="name" class="col-form-label">Customer CSV <span class="text-danger">*</span></label>
                <input id="csv" type="file" name="csv" class="form-control">
                @error('csv')
                    <span class="text-danger">{{$message}}</span>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button type="submit" class="btn btn-success">Submit</button>
            </div>
        </form>
    </div>
</div>



@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/summernote/summernote.min.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" />
@endpush

@push('scripts')
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{ asset('backend/summernote/summernote.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
    $('#lfm').filemanager('image');
</script>
@endpush
