<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InteractionLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'salesman_id', 'order_id', 'interaction_type',
        'notes', 'location_lat', 'location_lng', 'duration',
        'scheduled_followup', 'metadata'
    ];

    protected $casts = [
        'metadata' => 'array',
        'scheduled_followup' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function salesman()
    {
        return $this->belongsTo(User::class, 'salesman_id');
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
