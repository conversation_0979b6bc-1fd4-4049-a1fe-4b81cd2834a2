<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('price_level_lists', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->enum('price_type', ['fixed', 'percentage'])->default('fixed');
            $table->decimal('price_value', 8, 2)->default(0);
            $table->enum('method', ['plus','minus'])->default('plus');
            $table->enum('status' , ['active','inactive'])->default('inactive');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('price_level_lists');
    }
};
