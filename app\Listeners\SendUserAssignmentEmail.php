<?php

namespace App\Listeners;

use App\Events\UserAssignedToSalesman;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendUserAssignmentEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  UserAssignedToSalesman  $event
     * @return void
     */
    public function handle(UserAssignedToSalesman $event)
    {
        $customer = $event->customer;
        $salesman = $event->salesman;
        $previousSalesman = $event->previousSalesman;

        // Send email to customer about new salesman assignment
        $customerData = [
            'customer' => $customer,
            'salesman' => $salesman,
        ];

        Mail::to($customer->email, $customer->first_name . ' ' . $customer->last_name)
            ->queue(new MailQueue(
                'backend.emails.user_assigned_salesman_customer',
                $customerData,
                'Your New Sales Representative'
            ));

        // Send email to new salesman about customer assignment
        Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
            ->queue(new MailQueue(
                'backend.emails.user_assigned_salesman_notification',
                $customerData,
                'New Customer Assignment - ' . $customer->first_name . ' ' . $customer->last_name
            ));

        // If there was a previous salesman, notify them
        if ($previousSalesman) {
            // Note: This template would need to be created if reassignment notifications are needed
            // $previousSalesmanData = [
            //     'customer' => $customer,
            //     'new_salesman' => $salesman,
            // ];
            //
            // Mail::to($previousSalesman->email, $previousSalesman->first_name . ' ' . $previousSalesman->last_name)
            //     ->queue(new MailQueue(
            //         'backend.emails.user_reassigned_previous_salesman',
            //         $previousSalesmanData,
            //         'Customer Reassignment - ' . $customer->first_name . ' ' . $customer->last_name
            //     ));
        }
    }
}
