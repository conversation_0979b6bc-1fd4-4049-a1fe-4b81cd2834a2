@extends('frontend.layouts.master')

@section('title','Lamart || Register Page')

@section('main-content')
	<!-- Breadcrumbs -->
    <div class="breadcrumbs">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="bread-inner">
                        <ul class="bread-list">
                            <li><a href="{{route('home')}}">Home<i class="ti-arrow-right"></i></a></li>
                            <li class="active"><a href="javascript:void(0);">Register</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Breadcrumbs -->

    <!-- Shop Login -->
    <section class="shop login section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 offset-lg-2 col-12">
                    <div class="login-form">
                        <h2>Register</h2>
                        <p>Please register to access all features</p>
                        <!-- Form -->
                        <form class="form" method="post" id="resgistrationForm" action="{{route('register.submit')}}">
                            @csrf

                            <!-- Personal Information Section -->
                            <div class="form-section">
                                <h3>Personal Information</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>First Name<span>*</span></label>
                                            <input type="text" name="first_name" placeholder="John" required="required" value="{{old('first_name')}}">
                                            @error('first_name')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Last Name<span>*</span></label>
                                            <input type="text" name="last_name" placeholder="Doe" required="required" value="{{old('last_name')}}">
                                            @error('last_name')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label>Email Addresses<span>*</span></label>
                                            <input type="email" name="email[]" placeholder="" required="required" value="{{old('email.0')}}">
                                            @error('email.*')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                        <div id="additional_emails"></div>
                                        <button type="button" id="add_email" class="btn btn-secondary">Add Another Email</button>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Password<span>*</span></label>
                                            <input type="password" name="password" placeholder="********" required="required" autocomplete="password">
                                            @error('password')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Confirm Password<span>*</span></label>
                                            <input type="password" name="password_confirmation" placeholder="********" required="required" autocomplete="password_confirmation">
                                            @error('password_confirmation')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Company Information Section -->
                            <div class="form-section">
                                <h3>Company Information</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Company Name</label>
                                            <input type="text" name="company_name" placeholder="ABC Corp" value="{{old('company_name')}}">
                                            @error('company_name')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Billing Address Section -->
                            <div class="form-section">
                                <h3>Billing Address</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Street Address<span>*</span></label>
                                            <input type="text" id="billing_address" name="billing_address" placeholder="123 Main St" required="required" value="{{old('billing_address')}}">
                                            <input type="hidden" id="billing_lat" name="billing_lat" value="{{old('billing_lat')}}">
                                            <input type="hidden" id="billing_long" name="billing_long" value="{{old('billing_long')}}">
                                            @error('billing_address')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>City<span>*</span></label>
                                            <input type="text" id="billing_city" name="billing_city" placeholder="New York" required="required" value="{{old('billing_city')}}">
                                            @error('billing_city')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>State<span>*</span></label>
                                            <input type="text" id="billing_state" name="billing_state" placeholder="NY" required="required" value="{{old('billing_state')}}">
                                            @error('billing_state')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>ZIP Code<span>*</span></label>
                                            <input type="text" id="billing_zip" name="billing_zip" placeholder="10001" required="required" value="{{old('billing_zip')}}">
                                            @error('billing_zip')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Shipping Address Section -->
                            <div class="form-section">
                                <h3>Shipping Address</h3>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="same_as_billing_label">
                                                <input type="checkbox" id="same_as_billing" name="same_as_billing"> Check here if shipping address is the same as billing address
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Street Address<span>*</span></label>
                                            <input type="text" name="shipping_address" id="shipping_address" placeholder="123 Main St" required="required" value="{{old('shipping_address')}}">
                                            <input type="hidden" id="shipping_lat" name="shipping_lat" value="{{old('shipping_lat')}}">
                                            <input type="hidden" id="shipping_long" name="shipping_long" value="{{old('shipping_long')}}">
                                            @error('shipping_address')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>City<span>*</span></label>
                                            <input type="text" id="shipping_city" name="shipping_city" placeholder="New York" required="required" value="{{old('shipping_city')}}">
                                            @error('shipping_city')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>State<span>*</span></label>
                                            <input type="text" id="shipping_state" name="shipping_state" placeholder="NY" required="required" value="{{old('shipping_state')}}">
                                            @error('shipping_state')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>ZIP Code<span>*</span></label>
                                            <input type="text" id="shipping_zip" name="shipping_zip" placeholder="10001" required="required" value="{{old('shipping_zip')}}">
                                            @error('shipping_zip')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information Section -->
                            <div class="form-section">
                                <h3>Contact Information</h3>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Contact Phone Number<span>*</span></label>
                                            <input type="text" name="contact_phone" placeholder="****** 456 7890" required="required" value="{{old('contact_phone')}}">
                                            @error('contact_phone')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label>Account Payable Phone Number</label>
                                            <input type="text" name="account_phone" placeholder="****** 456 7890" value="{{old('account_phone')}}">
                                            @error('account_phone')
                                                <span class="text-danger">{{$message}}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div id="additional_phone_numbers" class="row"></div>
                                        @error('additional_phone_numbers.*')
                                            <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <button type="button" id="add_phone_number" class="btn btn-secondary">Add Another Phone Number</button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                <a role="button" href="javascript:void(0);" data-toggle="modal" data-target="#w9Modal" class="btn btn-secondary btn-zoom">W9 Form</a>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12">
                                <div class="form-group login-btn">
                                    <button class="btn" type="submit">Register</button>
                                    <a href="{{route('login.form')}}" class="btn">Login</a>
                                </div>
                            </div>
                        </form>
                        <!--/ End Form -->
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--/ End Login -->

    <div class="modal fade" id="w9Modal" tabindex="-1" aria-labelledby="w9ModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-gradient-primary">
                    <h5 class="modal-title fs-5 fw-semibold" id="w9ModalLabel">Request for Taxpayer Information</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body p-4">
                    @include('frontend.layouts.notification')
                    <form id="w9_form" action="{{ route('w9') }}" method="POST">
                        @csrf
                        <!-- Name Section -->
                        <div class="mb-4">
                            <label class="form-label fw-medium required" for="w9_name">Name (as shown on tax return)</label>
                            <input type="text" class="form-control py-1 px-2 rounded-2" name="name" id="w9_name" required>
                        </div>
                        <!-- Business Name Section -->
                        <div class="mb-4">
                            <label class="form-label fw-medium" for="w9_business_name">Business name (if different)</label>
                            <input type="text" class="form-control py-1 px-2 rounded-2" name="business_name" id="w9_business_name">
                        </div>
                        <!-- Tax Classification Section -->
                        <div class="mb-4 border rounded-3 p-4 bg-light tax-classification">
                            <h6 class="fw-semibold mb-3">Tax Classification</h6>
                            <div class="form-check mb-2 ms-4">
                                <input class="form-check-input" type="radio" name="taxClassification" id="individual" value="individual" required>
                                <label class="form-check-label fw-medium" for="individual">Individual/sole proprietor</label>
                            </div>
                            <div class="form-check mb-2 ms-4">
                                <input class="form-check-input" type="radio" name="taxClassification" id="c-corp" value="c-corp">
                                <label class="form-check-label fw-medium" for="c-corp">C Corporation</label>
                            </div>
                            <div class="form-check mb-2 ms-4">
                                <input class="form-check-input" type="radio" name="taxClassification" id="s-corp" value="s-corp">
                                <label class="form-check-label fw-medium" for="s-corp">S Corporation</label>
                            </div>
                            <div class="form-check mb-2 ms-4">
                                <input class="form-check-input" type="radio" name="taxClassification" id="partnership" value="partnership">
                                <label class="form-check-label fw-medium" for="partnership">Partnership</label>
                            </div>
                            <div class="form-check ms-4">
                                <input class="form-check-input" type="radio" name="taxClassification" id="trust" value="trust">
                                <label class="form-check-label fw-medium" for="trust">Trust/estate</label>
                            </div>
                        </div>
                        <!-- Address Section -->
                        <div class="mb-4">
                            <label class="form-label fw-medium required">Address</label>
                            <input type="text" class="form-control py-1 px-2 rounded-2 mb-3" id="streetAddress" name="streetAddress" placeholder="Street address" required>
                            <div class="row g-3">
                                <div class="col-md">
                                    <input type="text" class="form-control py-1 px-2 rounded-2" id="w9_city" name="city" placeholder="City" required>
                                </div>
                                <div class="col-md">
                                    <input type="text" class="form-control py-1 px-2 rounded-2" id="w9_state" name="state" placeholder="State" required>
                                </div>
                                <div class="col-md">
                                    <input type="text" class="form-control py-1 px-2 rounded-2" id="w9_zipCode" name="zipCode" placeholder="ZIP code" required>
                                </div>
                            </div>
                        </div>
                        <!-- TIN Section -->
                        <div class="mb-4">
                            <label class="form-label fw-medium required">Taxpayer Identification Number (TIN)</label>
                            <input type="text" class="form-control py-1 px-2 rounded-2" id="tin" name="tin" placeholder="SSN or EIN" required>
                        </div>
                        <!-- Footer Notes -->
                        <div class="text-muted small mb-3">
                            <p class="mb-1"><span class="text-danger">*</span> = Required field</p>
                            <p>Note: This is not an official IRS form. For official documents, please visit <a href="https://www.irs.gov" target="_blank" class="text-decoration-underline">irs.gov</a></p>
                        </div>
                        <!-- Form Actions -->
                        <div class="modal-footer border-0 pt-4 px-0">
                            <button type="button" class="btn btn-outline-secondary rounded-pill px-4" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary rounded-pill px-4 shadow-sm">Submit Form</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&libraries=places&v=weekly"></script>
<script>
    // JavaScript to handle "Same as Billing Address" checkbox
    document.getElementById('same_as_billing').addEventListener('change', function() {
        const shippingFields = ['shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip','shipping_lat','shipping_long'];
        const billingFields = ['billing_address', 'billing_city', 'billing_state', 'billing_zip', 'billing_lat','billing_long'];

        if (this.checked) {
            shippingFields.forEach((field, index) => {
                document.querySelector(`input[name="${field}"]`).value = document.querySelector(`input[name="${billingFields[index]}"]`).value;
            });
        } else {
            shippingFields.forEach(field => {
                document.querySelector(`input[name="${field}"]`).value = '';
            });
        }
    });

    document.getElementById('add_email').addEventListener('click', function() {
        const emailDiv = document.createElement('div');
        emailDiv.classList.add('form-group');
        emailDiv.innerHTML = `
            <input type="email" name="email[]" placeholder="Additional Email" value="">
        `;
        document.getElementById('additional_emails').appendChild(emailDiv);
    });

    document.getElementById('add_phone_number').addEventListener('click', function () {
        const wrapper = document.createElement('div');
        wrapper.classList.add('col-md-6');

        const formGroup = document.createElement('div');
        formGroup.classList.add('form-group');

        const input = document.createElement('input');
        input.type = 'text';
        input.name = 'additional_phone_numbers[]';
        input.placeholder = '****** 456 7890';
        input.classList.add('form-control');

        formGroup.appendChild(input);
        wrapper.appendChild(formGroup);

        document.getElementById('additional_phone_numbers').appendChild(wrapper);
    });

    $(document).ready(function () {
    $.validator.addMethod("tinFormat", function(value, element) {
        return this.optional(element) || /^\d{3}-\d{2}-\d{4}$/.test(value) || /^\d{2}-\d{7}$/.test(value);
    }, "Please enter a valid SSN (XXX-XX-XXXX) or EIN (XX-XXXXXXX)");

    $.validator.addMethod("exactlyOneTaxClass", function() {
        return $('.tax-classification input:checked').length === 1;
    }, "Please select exactly one tax classification");

    $("#w9_form").validate({
        rules: {
            name: "required",
            business_name: { required: false },
            streetAddress: "required",
            city: "required",
            state: {
                required: true,
                minlength: 2,
                maxlength: 2
            },
            zipCode: {
                required: true,
                digits: true,
                minlength: 5,
                maxlength: 5
            },
            tin: {
                required: true,
                tinFormat: true
            },
            taxClassification: {
                exactlyOneTaxClass: true
            }
        },
        messages: {
            name: "Please enter your full name",
            streetAddress: "Please enter your street address",
            city: "Please enter your city",
            state: {
                required: "Please enter state",
                minlength: "Use 2-letter abbreviation",
                maxlength: "Use 2-letter abbreviation"
            },
            zipCode: {
                required: "Please enter ZIP code",
                digits: "ZIP must be numbers only",
                minlength: "ZIP must be 5 digits",
                maxlength: "ZIP must be 5 digits"
            },
            tin: {
                required: "Please enter your TIN",
                tinFormat: "Invalid format (use XXX-XX-XXXX or XX-XXXXXXX)"
            }
        },
        errorPlacement: function(error, element) {
            if (element.attr("name") === "taxClassification") {
                error.insertAfter(".tax-classification");
            } else {
                error.insertAfter(element);
            }
        },
        submitHandler: function(form) {
            $.ajax({
                url: '{{ route("w9") }}',
                type: 'POST',
                data: $(form).serialize(), // Serialize form data including CSRF token
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    successtoast('W9 submitted successfully!');
                    $('#w9Modal').modal('hide');
                    form.reset();
                },
                error: function(xhr) {
                    errortoast(xhr.responseJSON?.message || 'Something went wrong');
                }
            });
        }
    });
});
</script>
<script>
    function initAutocomplete() {
        var input = document.getElementById('billing_address');
        var autocomplete = new google.maps.places.Autocomplete(input);

        var input1 = document.getElementById('shipping_address');
        var autocomplete1 = new google.maps.places.Autocomplete(input1);

        autocomplete.addListener('place_changed', function () {
            var place = autocomplete.getPlace();
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();

            // Store in hidden fields or log it
            document.getElementById("billing_lat").value = lat;
            document.getElementById("billing_long").value = lng;
            place.address_components.forEach(component => {
                var types = component.types;
                if (types.includes("locality")) {
                    document.getElementById('billing_city').value = component.long_name;
                }
                if (types.includes("administrative_area_level_1")) {
                    document.getElementById('billing_state').value = component.long_name;
                }
                if (types.includes("postal_code")) {
                    document.getElementById('billing_zip').value = component.long_name;
                }
            });
        });

        autocomplete1.addListener('place_changed', function () {
            var place = autocomplete1.getPlace();
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();

            // Store in hidden fields or log it
            document.getElementById("shipping_lat").value = lat;
            document.getElementById("shipping_long").value = lng;
            place.address_components.forEach(component => {
                var types = component.types;
                if (types.includes("locality")) {
                    document.getElementById('shipping_city').value = component.long_name;
                }
                if (types.includes("administrative_area_level_1")) {
                    document.getElementById('shipping_state').value = component.long_name;
                }
                if (types.includes("postal_code")) {
                    document.getElementById('shipping_zip').value = component.long_name;
                }
            });
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        initAutocomplete();
    });
</script>
<script>

 </script>
@endpush

@push('styles')
<style>
    .shop.login .form .btn {
        margin-right: 0;
    }
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .form-section h3 {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
    .same_as_billing_label {
        display: flex;
        align-items: center;
    }
    #same_as_billing {
        width: 15px;
        margin-right: 10px;
    }

    .error {
            color : red !important;
        }
</style>
@endpush
