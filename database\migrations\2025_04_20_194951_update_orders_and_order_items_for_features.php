<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->boolean('is_paid')->default(false);
            $table->boolean('is_printed')->default(false);
            $table->boolean('safe_for_future')->default(false);
            $table->string('delivery_method')->nullable(); // pickup, delivery, ship
            $table->string('packing_slip_url')->nullable();
        });
    
        Schema::table('order_items', function (Blueprint $table) {
            $table->string('return_status')->default('none'); // returned, damaged
            $table->timestamp('returned_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['is_paid', 'is_printed', 'safe_for_future', 'delivery_method', 'packing_slip_url']);
        });
    
        Schema::table('order_items', function (Blueprint $table) {
            $table->dropColumn(['return_status', 'returned_at']);
        });
    }
};
