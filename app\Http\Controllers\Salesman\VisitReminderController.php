<?php

namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use App\Models\VisitReminder;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class VisitReminderController extends Controller
{
    public function index() {
        $reminders = VisitReminder::where('salesman_id',Auth::id())->get();
        // dd($reminders);
        return view('salesman.reminders.index', compact('reminders'));
    }

    public function create()
    {
        $customers = Auth::user()->assignedCustomers();
        return view('salesman.reminders.create', compact('customers'));
    }

    public function store(Request $req)
    {
        $data = $req->validate([
          'customer_id'=>'required|exists:users,id',
          'visit_date'=>'required|date',
          'note'=>'nullable|string',
          'is_recurring'=>'boolean',
          'interval'=>'required_if:is_recurring,1|integer|min:1',
          'frequency'=>'required_if:is_recurring,1|in:day,week,month,year',
        ]);
        $data['salesman_id'] = Auth::id();
        VisitReminder::create($data);
        return redirect()->route('salesman.visit.index')->with('success','Reminder created');
    }

    public function edit($id)
    {
        $reminder = VisitReminder::find($id);
        $customers = Auth::user()->assignedCustomers();
        return view('salesman.reminders.edit', compact('reminder','customers'));
    }

    public function update(Request $req, $id)
    {
        $data = $req->validate([
          'customer_id'=>'required|exists:users,id',
          'visit_date'=>'required|date',
          'note'=>'nullable|string',
          'is_recurring'=>'boolean',
          'interval'=>'required_if:is_recurring,1|integer|min:1',
          'frequency'=>'required_if:is_recurring,1|in:day,week,month,year',
        ]);
        VisitReminder::find($id)->update($data);
        return redirect()->route('salesman.visit.index')->with('success','Reminder updated');
    }

    public function destroy($id)
    {
        $reminder = VisitReminder::find($id);
        $reminder->delete();
        return redirect()->route('salesman.visit.index')->with('success','Reminder deleted');
    }
}
