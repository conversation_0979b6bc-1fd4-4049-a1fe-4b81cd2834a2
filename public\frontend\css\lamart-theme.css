/* =====================================
LAMART - MODERN ECOMMERCE THEME
Primary Color: #4D734E (Forest Green)
Secondary Color: #5A5863 (Charcoal Grey)
Accent Color: #000000 (Pure Black)
Light Grey: #F8F9FA (Background)
Success: #28A745 (Success Green)
Warning: #FFC107 (Warning Amber)
========================================*/

/* CSS Variables for consistent theming - LAMART BRAND COLORS */
:root {
    --primary-color: #4d734e; /* Brand Green from Logo */
    --primary-dark: #3d5a3e; /* Darker Green for hover states */
    --primary-light: #6b9a6d; /* Lighter Green for backgrounds */
    --secondary-color: #5a5863; /* Brand Grey from Logo */
    --secondary-light: #8a8a95; /* Lighter Grey */
    --secondary-dark: #4a4a52; /* Darker Grey */
    --accent-color: #000000; /* Brand <PERSON> from Logo */
    --light-bg: #f8f9fa; /* Light background */
    --white: #ffffff; /* Pure white */
    --success: #4d734e; /* Use brand green for success */
    --warning: #ffc107; /* Warning amber */
    --danger: #dc3545; /* Danger red */
    --border-light: #e9ecef; /* Light borders */
    --text-dark: #212529; /* Dark text */
    --text-muted: #5a5863; /* Muted text using brand grey */
    --shadow-sm: 0 0.125rem 0.25rem rgba(77, 115, 78, 0.1);
    --shadow: 0 0.5rem 1rem rgba(77, 115, 78, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(77, 115, 78, 0.2);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #4d734e, #3d5a3e);
    --gradient-secondary: linear-gradient(135deg, #5a5863, #4a4a52);
}

/* =====================================
MODERN PRELOADER & LOADING STATES
========================================*/

/* Modern Preloader Override */
.preloader {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999999999;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--white), var(--light-bg));
    overflow: hidden;
}

.preloader-inner {
    position: relative;
    text-align: center;
}

.preloader-icon {
    width: 100%;
    height: 80px;
    display: inline-block;
    position: relative;
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
}

.preloader-icon span {
    position: absolute;
    display: inline-block;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--gradient-primary);
    animation: modern-preloader 1.6s cubic-bezier(0.4, 0, 0.2, 1) infinite;
    box-shadow: var(--shadow);
}

@keyframes modern-preloader {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

/* Preloader Text */
.preloader-inner::after {
    content: "LAMART";
    display: block;
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: 3px;
    margin-top: 20px;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%,
    100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

/* Alternative Modern Spinner */
.modern-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--border-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Loading Dots */
.loading-dots {
    display: flex;
    gap: 8px;
    justify-content: center;
    align-items: center;
}

.loading-dots span {
    width: 12px;
    height: 12px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}
.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}
.loading-dots span:nth-child(3) {
    animation-delay: 0s;
}

@keyframes loading-bounce {
    0%,
    80%,
    100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* =====================================
MODERN HEADER STYLING
========================================*/

/* Enhanced Header */
.header.shop {
    box-shadow: var(--shadow);
    background: var(--white);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header.shop .topbar {
    background: linear-gradient(
        135deg,
        var(--primary-color),
        var(--primary-dark)
    );
    padding: 8px 0;
    font-size: 13px;
}

.header.shop .topbar .list-main li {
    color: var(--white);
    margin-right: 20px;
}

.header.shop .topbar .list-main li i {
    color: var(--white);
    margin-right: 8px;
    font-size: 14px;
}

.header.shop .topbar .list-main li a {
    color: var(--white);
    transition: var(--transition);
    text-decoration: none;
}

.header.shop .topbar .list-main li a:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Logo Enhancement */
.logo img {
    max-height: 60px;
    width: auto;
    transition: var(--transition);
}

.logo:hover img {
    transform: scale(1.05);
}

/* Modern Search Bar */
.search-bar-top .search-bar {
    background: var(--white);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.search-bar-top .search-bar:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.search-bar-top .search-bar select {
    border: none;
    background: var(--light-bg);
    padding: 12px 15px;
    font-weight: 500;
    color: var(--text-dark);
}

.search-bar-top .search-bar input {
    border: none;
    padding: 12px 15px;
    font-size: 14px;
    flex: 1;
}

.search-bar-top .search-bar .btnn {
    background: var(--primary-color);
    border: none;
    padding: 12px 20px;
    color: var(--white);
    transition: var(--transition);
}

.search-bar-top .search-bar .btnn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* Shopping Cart Icon */
.right-bar .shopping {
    position: relative;
}

.right-bar .shopping .dropdown-cart-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.right-bar .shopping .dropdown-cart-header:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.right-bar .shopping .total-count {
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-left: 5px;
}

/* =====================================
MODERN PRODUCT CARDS
========================================*/

/* Enhanced Product Cards */
.single-product {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 30px;
    border: 1px solid var(--border-light);
}

.single-product:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.single-product .product-img {
    position: relative;
    overflow: hidden;
}

.single-product .product-img img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.single-product:hover .product-img img {
    transform: scale(1.05);
}

/* Product Action Buttons */
.single-product .product-img .product-action {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    opacity: 0;
    transform: translateX(20px);
    transition: var(--transition);
}

.single-product:hover .product-img .product-action {
    opacity: 1;
    transform: translateX(0);
}

.single-product .product-img .product-action a {
    width: 40px;
    height: 40px;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-dark);
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-decoration: none;
}

.single-product .product-img .product-action a:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Product Content */
.single-product .product-content {
    padding: 20px;
}

.single-product .product-content h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    line-height: 1.4;
}

.single-product .product-content h3 a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
}

.single-product .product-content h3 a:hover {
    color: var(--primary-color);
}

/* Product Price */
.single-product .product-content .product-price {
    margin-bottom: 15px;
}

.single-product .product-content .product-price span {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.single-product .product-content .product-price .old-price {
    font-size: 14px;
    color: var(--text-muted);
    text-decoration: line-through;
    margin-left: 8px;
    font-weight: 400;
}

/* Product Rating */
.single-product .product-content .rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 15px;
}

.single-product .product-content .rating i {
    color: #ffd700;
    font-size: 14px;
}

.single-product .product-content .rating .rating-count {
    font-size: 12px;
    color: var(--text-muted);
    margin-left: 5px;
}

/* Add to Cart Button */
.single-product .product-content .add-to-cart {
    width: 100%;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.single-product .product-content .add-to-cart:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* =====================================
MODERN BUTTONS & FORMS
========================================*/

/* Primary Buttons */
.btn {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
    color: var(--white);
}

.btn.btn-secondary {
    background: var(--secondary-color);
}

.btn.btn-secondary:hover {
    background: var(--secondary-light);
}

.btn.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Form Elements */
.form-control,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
select {
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    font-size: 14px;
    transition: var(--transition);
    background: var(--white);
}

.form-control:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

/* Modern Pagination */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination .pagination-list {
    display: flex;
    gap: 5px;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination .pagination-list li a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.pagination .pagination-list li.active a,
.pagination .pagination-list li:hover a {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* =====================================
MODERN NAVIGATION & MENU
========================================*/

/* Main Navigation */
.header.shop .header-inner {
    background: var(--white);
    padding: 15px 0;
    border-bottom: 1px solid var(--border-light);
}

.header.shop .nav li a {
    color: var(--text-dark);
    font-weight: 500;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    text-decoration: none;
}

.header.shop .nav li:hover a,
.header.shop .nav li.active a {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

/* Dropdown Menus */
.header.shop .nav li .dropdown {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    padding: 10px 0;
    margin-top: 10px;
}

.header.shop .nav li .dropdown li a {
    padding: 8px 20px;
    color: var(--text-dark);
    border-radius: 0;
}

.header.shop .nav li .dropdown li:hover a {
    background: var(--light-bg);
    color: var(--primary-color);
    transform: none;
}

/* =====================================
MODERN SIDEBAR & FILTERS
========================================*/

/* Sidebar Widgets */
.shop-sidebar .single-widget,
.main-sidebar .single-widget {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shop-sidebar .single-widget h3,
.main-sidebar .single-widget h3 {
    background: var(--primary-color);
    color: var(--white);
    margin: -25px -25px 20px -25px;
    padding: 15px 25px;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    font-size: 16px;
    font-weight: 600;
}

/* Category List */
.shop-sidebar .categor-list li,
.main-sidebar .categor-list li {
    border-bottom: 1px solid var(--border-light);
    padding: 10px 0;
}

.shop-sidebar .categor-list li:last-child,
.main-sidebar .categor-list li:last-child {
    border-bottom: none;
}

.shop-sidebar .categor-list li a,
.main-sidebar .categor-list li a {
    color: var(--text-dark);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.shop-sidebar .categor-list li a:hover,
.main-sidebar .categor-list li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

/* Price Range Slider */
.shop .range {
    margin: 20px 0;
}

.shop .range #slider-range {
    background: var(--border-light);
    border-radius: var(--border-radius);
    height: 6px;
}

.shop .range .ui-slider-handle {
    background: var(--primary-color);
    border: 3px solid var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    box-shadow: var(--shadow);
    cursor: pointer;
}

.shop .range .ui-slider-range {
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

/* =====================================
MODERN SHOPPING CART & CHECKOUT
========================================*/

/* Shopping Cart Table */
.shopping-summery {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shopping-summery thead {
    background: var(--primary-color);
    color: var(--white);
}

.shopping-summery thead th {
    padding: 15px;
    font-weight: 600;
    border: none;
}

.shopping-summery tbody td {
    padding: 20px 15px;
    border-bottom: 1px solid var(--border-light);
    vertical-align: middle;
}

.shopping-summery tbody tr:last-child td {
    border-bottom: none;
}

.shopping-summery .product img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.shopping-summery .product-name a {
    color: var(--text-dark);
    font-weight: 500;
    text-decoration: none;
    transition: var(--transition);
}

.shopping-summery .product-name a:hover {
    color: var(--primary-color);
}

/* Quantity Controls */
.shopping-cart .qty {
    display: flex;
    align-items: center;
    gap: 10px;
}

.shopping-cart .qty .button {
    display: flex;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.shopping-cart .qty .button input {
    width: 60px;
    text-align: center;
    border: none;
    padding: 8px;
    font-weight: 500;
}

.shopping-cart .qty .button .btn {
    width: 35px;
    height: 35px;
    background: var(--light-bg);
    color: var(--text-dark);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    padding: 0;
}

.shopping-cart .qty .button .btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Cart Total */
.shopping-cart .total-amount {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    padding: 30px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.shopping-cart .total-amount h3 {
    color: var(--text-dark);
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--primary-color);
}

.shopping-cart .total-amount .total-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.shopping-cart .total-amount .total-list li {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-light);
}

.shopping-cart .total-amount .total-list li:last-child {
    border-bottom: none;
    font-weight: 600;
    font-size: 18px;
    color: var(--primary-color);
}

/* Coupon Form */
.shopping-cart .total-amount .coupon form {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.shopping-cart .total-amount .coupon input {
    flex: 1;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 12px 15px;
}

.shopping-cart .total-amount .coupon .btn {
    padding: 12px 20px;
    white-space: nowrap;
}

/* =====================================
MODERN FOOTER
========================================*/

.footer {
    background: linear-gradient(135deg, var(--text-dark), #1a1a1a);
    color: var(--white);
    padding: 60px 0 20px;
    margin-top: 80px;
}

.footer .single-footer {
    margin-bottom: 40px;
}

.footer .single-footer h4 {
    color: var(--white);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 10px;
}

.footer .single-footer h4::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: var(--border-radius);
}

.footer .single-footer p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 20px;
}

.footer .single-footer .links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer .single-footer .links ul li {
    margin-bottom: 10px;
}

.footer .single-footer .links ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.footer .single-footer .links ul li a:hover {
    color: var(--primary-color);
    padding-left: 10px;
}

.footer .single-footer .links ul li a i {
    font-size: 14px;
}

/* Social Links */
.footer .social ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    gap: 15px;
}

.footer .social ul li a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    text-decoration: none;
}

.footer .social ul li a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow);
}

/* Footer Bottom */
.footer .copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 30px;
    margin-top: 40px;
    text-align: center;
}

.footer .copyright p {
    color: rgba(255, 255, 255, 0.6);
    margin: 0;
}

/* =====================================
MODERN ALERTS & NOTIFICATIONS
========================================*/

.alert {
    border-radius: var(--border-radius-lg);
    padding: 15px 20px;
    margin-bottom: 20px;
    border: none;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: var(--shadow-sm);
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border-left: 4px solid var(--success);
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border-left: 4px solid var(--danger);
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: var(--warning);
    border-left: 4px solid var(--warning);
}

.alert-info {
    background: rgba(77, 115, 78, 0.1);
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

/* =====================================
RESPONSIVE DESIGN ENHANCEMENTS
========================================*/

@media (max-width: 768px) {
    .header.shop .topbar {
        padding: 5px 0;
        font-size: 12px;
    }

    .header.shop .topbar .list-main li {
        margin-right: 10px;
    }

    .search-bar-top .search-bar {
        flex-direction: column;
    }

    .search-bar-top .search-bar select,
    .search-bar-top .search-bar input {
        width: 100%;
    }

    .single-product .product-img img {
        height: 200px;
    }

    .shopping-summery {
        font-size: 14px;
    }

    .shopping-summery .product img {
        width: 60px;
        height: 60px;
    }

    .footer {
        padding: 40px 0 20px;
        text-align: center;
    }

    .footer .social ul {
        justify-content: center;
    }
}

/* =====================================
UTILITY CLASSES
========================================*/

.text-primary {
    color: var(--primary-color) !important;
}
.text-secondary {
    color: var(--secondary-color) !important;
}
.bg-primary {
    background-color: var(--primary-color) !important;
}
.bg-secondary {
    background-color: var(--secondary-color) !important;
}
.bg-light {
    background-color: var(--light-bg) !important;
}

.shadow-sm {
    box-shadow: var(--shadow-sm) !important;
}
.shadow {
    box-shadow: var(--shadow) !important;
}
.shadow-lg {
    box-shadow: var(--shadow-lg) !important;
}

.rounded {
    border-radius: var(--border-radius) !important;
}
.rounded-lg {
    border-radius: var(--border-radius-lg) !important;
}

.transition {
    transition: var(--transition) !important;
}

/* =====================================
MODERN HERO SLIDER & BANNERS
========================================*/

/* Hero Slider Enhancement */
#Gslider {
    position: relative;
    overflow: hidden;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

#Gslider .carousel-item {
    position: relative;
    height: 500px;
    background: var(--gradient-primary);
}

#Gslider .carousel-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

#Gslider .carousel-item::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(0, 0, 0, 0.6),
        rgba(77, 115, 78, 0.3)
    );
    z-index: 1;
}

#Gslider .carousel-caption {
    position: absolute;
    top: 50%;
    left: 50px;
    transform: translateY(-50%);
    z-index: 2;
    text-align: left !important;
    max-width: 500px;
}

#Gslider .carousel-caption h1 {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

#Gslider .carousel-caption p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 30px;
    line-height: 1.6;
}

#Gslider .carousel-caption .btn {
    background: var(--primary-color);
    color: var(--white);
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    box-shadow: var(--shadow);
}

#Gslider .carousel-caption .btn:hover {
    background: var(--white);
    color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* Carousel Controls */
#Gslider .carousel-control-prev,
#Gslider .carousel-control-next {
    width: 60px;
    height: 60px;
    background: rgba(77, 115, 78, 0.8);
    border-radius: 50%;
    top: 50%;
    transform: translateY(-50%);
    transition: var(--transition);
}

#Gslider .carousel-control-prev {
    left: 30px;
}

#Gslider .carousel-control-next {
    right: 30px;
}

#Gslider .carousel-control-prev:hover,
#Gslider .carousel-control-next:hover {
    background: var(--primary-color);
    transform: translateY(-50%) scale(1.1);
}

/* Carousel Indicators */
#Gslider .carousel-indicators {
    bottom: 30px;
}

#Gslider .carousel-indicators li {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid var(--white);
    margin: 0 5px;
    transition: var(--transition);
}

#Gslider .carousel-indicators li.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

/* Small Banners Enhancement */
.small-banner {
    padding: 80px 0;
    background: var(--light-bg);
}

.small-banner .single-banner {
    background: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
    margin-bottom: 30px;
    border: 1px solid var(--border-light);
    position: relative;
}

.small-banner .single-banner:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.small-banner .single-banner img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: var(--transition);
}

.small-banner .single-banner:hover img {
    transform: scale(1.05);
}

.small-banner .single-banner .content {
    padding: 30px;
    text-align: center;
}

.small-banner .single-banner h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 15px;
}

.small-banner .single-banner h3 span {
    color: var(--primary-color);
}

.small-banner .single-banner p {
    color: var(--text-muted);
    margin-bottom: 20px;
    line-height: 1.6;
}

.small-banner .single-banner a {
    background: var(--primary-color);
    color: var(--white);
    padding: 12px 25px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    display: inline-block;
}

.small-banner .single-banner a:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* =====================================
MODERN PRODUCT SECTIONS
========================================*/

/* Product Area Tabs */
.product-area {
    padding: 80px 0;
    background: var(--white);
}

.product-area .section-title {
    text-align: center;
    margin-bottom: 60px;
}

.product-area .section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 15px;
    position: relative;
}

.product-area .section-title h2::after {
    content: "";
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius);
}

.product-area .section-title p {
    color: var(--text-muted);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* Product Tabs Navigation */
.product-area .nav-tabs {
    border: none;
    justify-content: center;
    margin-bottom: 50px;
}

.product-area .nav-tabs .nav-item {
    margin: 0 10px;
}

.product-area .nav-tabs .nav-link {
    background: var(--light-bg);
    color: var(--text-dark);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    padding: 12px 25px;
    font-weight: 600;
    transition: var(--transition);
    text-decoration: none;
}

.product-area .nav-tabs .nav-link:hover,
.product-area .nav-tabs .nav-link.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

/* Product Grid Enhancement */
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

/* Enhanced Product Cards (already defined above, but adding specific overrides) */
.single-product .product-content .product-price .price {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.single-product .product-content .product-price .old-price {
    font-size: 1rem;
    color: var(--text-muted);
    text-decoration: line-through;
    margin-left: 8px;
}

/* Sale Badge */
.single-product .sale-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--danger);
    color: var(--white);
    padding: 5px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

.single-product .new-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--success);
    color: var(--white);
    padding: 5px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
}

/* Responsive Design for Products */
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .product-area .section-title h2 {
        font-size: 2rem;
    }

    .product-area .nav-tabs {
        flex-wrap: wrap;
    }

    .product-area .nav-tabs .nav-item {
        margin: 5px;
    }
}

/* =====================================
MODERN RESPONSIVE HERO DESIGN
========================================*/

/* Responsive Design for Hero */
@media (max-width: 768px) {
    #Gslider .carousel-item {
        height: 400px;
    }

    #Gslider .carousel-caption {
        left: 20px;
        right: 20px;
        max-width: none;
    }

    #Gslider .carousel-caption h1 {
        font-size: 2.5rem;
    }

    #Gslider .carousel-caption p {
        font-size: 1rem;
    }

    #Gslider .carousel-control-prev,
    #Gslider .carousel-control-next {
        width: 50px;
        height: 50px;
    }

    #Gslider .carousel-control-prev {
        left: 15px;
    }

    #Gslider .carousel-control-next {
        right: 15px;
    }
}

@media (max-width: 480px) {
    #Gslider .carousel-item {
        height: 350px;
    }

    #Gslider .carousel-caption h1 {
        font-size: 2rem;
    }

    #Gslider .carousel-caption .btn {
        padding: 12px 20px;
        font-size: 1rem;
    }
}

/* =====================================
MODERN LOADING STATES & ANIMATIONS
========================================*/

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(77, 115, 78, 0.3);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Slide Up Animation */
.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* =====================================
MODERN ACCESSIBILITY ENHANCEMENTS
========================================*/

/* Focus States */
.btn:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.3);
        --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4);
        --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.5);
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* =====================================
MODERN PRINT STYLES
========================================*/

@media print {
    .header,
    .footer,
    .btn,
    .carousel-control-prev,
    .carousel-control-next,
    .carousel-indicators {
        display: none !important;
    }

    .single-product {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}
