<?php

namespace App\Listeners;

use App\Events\UserCreatedByAdmin;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendUserCreatedByAdminEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\UserCreatedByAdmin  $event
     * @return void
     */
    public function handle(UserCreatedByAdmin $event)
    {
        $user = $event->user;
        $password = $event->password;

        $data = [
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'password' => $password,
        ];

        Mail::to($user->email)->queue(new MailQueue(
            'emails.created',
            $data,
            'Account Created - Lamart Manufacturing'
        ));
    }
}
