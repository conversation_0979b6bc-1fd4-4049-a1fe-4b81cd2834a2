<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Color;

class ColorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Color::select('id', 'name', 'color', 'status')->get();
            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('name', function ($row) {
                    return $row->name;
                })
                ->addColumn('color', function ($row) {
                    return $row->color;
                })
                ->addColumn('status', function ($row) {
                    if ($row->status == 'active') {
                        return '<span class="badge badge-success">Active</span>';
                    } else {
                        return '<span class="badge badge-warning">Inactive</span>';
                    }
                })
                ->addColumn('action', function ($row) {
                    $editBtn = '<a href="' . route('color.edit', $row->id) . '" class="btn btn-primary btn-sm mr-1" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';
                    $deleteBtn = '<form method="POST" action="' . route('color.destroy', $row->id) . '" style="display:inline">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                    <button type="submit" class="btn btn-danger btn-sm dltBtn" data-toggle="tooltip" title="Delete"><i class="fas fa-trash-alt"></i></button>
                                </form>';
                    return $editBtn . $deleteBtn;
                })
                ->rawColumns(['status', 'action'])
                ->make(true);
        }

        return view('backend.color.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backend.color.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string',
            'color' => ['required', 'regex:/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/'],
            'status' => 'required|in:active,inactive',
        ]);

        $color = new Color;
        $color->name = $validatedData['name'];
        $color->color = $validatedData['color'];
        $color->status = $validatedData['status'];
        $color->save();

        $message = $color
            ? 'Color Successfully added'
            : 'Please try again!!';

        if ($request->ajax()) {
            return response()->json(['id' => $color->id, 'name' => $color->name]);
        } else{
            return redirect()->route('color.index')->with(
                $color ? 'success' : 'error',
                $message
            );
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $color = Color::find($id);
        return view('backend.color.edit', compact('color'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validatedData = $request->validate([
            'name' => 'required|string',
            'color' => ['required', 'regex:/^#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/'],
            'status' => 'required|in:active,inactive',
        ]);

        $color = Color::find($id);
        $color->name = $validatedData['name'];
        $color->color = $validatedData['color'];
        $color->status = $validatedData['status'];
        $color = $color->update();

        $message = $color
            ? 'Color Successfully Updated'
            : 'Please try again!!';

        return redirect()->route('color.index')->with(
            $color ? 'success' : 'error',
            $message
        );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $color = Color::find($id);
        $color = $color->delete();

        $message = $color
            ? 'Color Successfully Deleted'
            : 'Error While Deleting Color!!';

        return redirect()->route('color.index')->with(
            $color ? 'success' : 'error',
            $message
        );
    }
}
