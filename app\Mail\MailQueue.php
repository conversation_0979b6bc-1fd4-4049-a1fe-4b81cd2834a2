<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\Middleware\RateLimited;

class MailQueue extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * View Data for the Email
     *
     * @var array
     **/
    protected $data;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of unhandled exceptions to allow before failing.
     *
     * @var int
     */
    public $maxExceptions = 2;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * Create a new message instance.
     *
     * @param array $data
     * @param string $queue
     * @return void
     */
    public function __construct($data, $queue = 'emails')
    {
        $this->data = $data;
        $this->onQueue($queue);

        // Set priority based on email type
        if (isset($data['priority'])) {
            switch ($data['priority']) {
                case 'high':
                    $this->onQueue('high');
                    break;
                case 'low':
                    $this->onQueue('low');
                    break;
                default:
                    $this->onQueue('emails');
            }
        }
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    public function middleware()
    {
        return [new RateLimited('emails')];
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        \Log::error('Email failed to send', [
            'subject' => $this->data['subject'] ?? 'Unknown',
            'view_file' => $this->data['view_file'] ?? 'Unknown',
            'error' => $exception->getMessage(),
            'data' => $this->data
        ]);
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        $email = $this->view($this->data['view_file'])
                ->subject($this->data['subject'])
                ->with($this->data);

        return $email;
    }
}
