<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisitReminder extends Model
{
    use HasFactory;

    protected $table = 'visit_reminders';

    protected $fillable = [
        'salesman_id','customer_id','visit_date','note','is_recurring', 'frequency', 'interval'
      ];

      public function salesman() { return $this->belongsTo(User::class, 'salesman_id'); }
      public function customer() { return $this->belongsTo(User::class, 'customer_id'); }
}
