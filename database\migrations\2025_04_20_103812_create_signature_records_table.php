<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('signature_records', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->enum('signature_type', ['pre_delivery', 'on_delivery']);
            $table->text('signature_data')->comment('Base64 encoded signature image');
            $table->string('signature_ip')->nullable();
            $table->string('device_info')->nullable();
            $table->timestamp('signed_at')->useCurrent();
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
        });

        DB::statement('CREATE INDEX idx_signature_order_type ON signature_records (order_id, signature_type)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('signature_records');
    }
};
