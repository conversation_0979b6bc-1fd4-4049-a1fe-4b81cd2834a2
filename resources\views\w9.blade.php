<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>W9-Style Information Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .form-container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .form-section {
            margin-bottom: 25px;
        }

        .form-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #444;
        }

        input[type="text"],
        input[type="number"] {
            width: 100%;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .tax-classification {
            border: 1px solid #ccc;
            padding: 15px;
            margin-bottom: 20px;
        }

        .checkbox-group {
            margin: 10px 0;
        }

        .address-group {
            display: flex;
            gap: 15px;
        }

        .address-group div {
            flex: 1;
        }

        .required::after {
            content: "*";
            color: red;
            margin-left: 3px;
        }

        .disclaimer {
            font-size: 0.9em;
            color: #666;
            margin-top: 20px;
        }

        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .error {
            color : red !important;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
</head>
<body>
    <div class="form-container">
	@include('frontend.layouts.notification')
        <h2 class="form-title">Request for Taxpayer Information</h2>
        
        <form id="w9_form" action="{{route('w9')}}" method="POST">
            @csrf
            <div class="form-section">
                <label class="required" name="name">Name (as shown on tax return)</label>
                <input type="text" name="name" id="name" required>
            </div>

            <div class="form-section">
                <label>Business name (if different)</label>
                <input type="text" name="business_name">
            </div>

            <div class="tax-classification">
                <h3>Tax Classification</h3>
                <div class="checkbox-group">
                    <input type="checkbox" id="individual" value="individual" name="taxClassification">
                    <label for="individual">Individual/sole proprietor</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="c-corp" value="c-corp" name="taxClassification">
                    <label for="c-corp">C Corporation</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="s-corp" value="s-corp" name="taxClassification">
                    <label for="s-corp">S Corporation</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="partnership" value="partnership" name="taxClassification">
                    <label for="partnership">Partnership</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="trust" value="trust" name="taxClassification">
                    <label for="trust">Trust/estate</label>
                </div>
            </div>

            <div class="form-section">
                <label class="required">Address</label>
                <input type="text" name="streetAddress" placeholder="Street address" required>
                <div class="address-group">
                    <div>
                        <input type="text" name="city" placeholder="City" required>
                    </div>
                    <div>
                        <input type="text" name="state" placeholder="State" required>
                    </div>
                    <div>
                        <input type="text" name="zipCode" placeholder="ZIP code" required>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <label class="required">Taxpayer Identification Number (TIN)</label>
                <input type="text" name="tin" placeholder="SSN or EIN" required>
            </div>

            <div class="disclaimer">
                <p>* = Required field</p>
                <p>Note: This is not an official IRS form. For official documents, please visit <a href="https://www.irs.gov">irs.gov</a></p>
            </div>

            <button type="submit">Submit Form</button>
        </form>
    </div>
</body>
<script>
$(document).ready(function() {
    // Custom method for TIN validation (SSN or EIN format)
    $.validator.addMethod("tinFormat", function(value, element) {
        return this.optional(element) || /^\d{3}-\d{2}-\d{4}$/.test(value) ||  // SSN
               /^\d{2}-\d{7}$/.test(value);  // EIN
    }, "Please enter a valid SSN (XXX-XX-XXXX) or EIN (XX-XXXXXXX)");

    // Custom method for exactly one tax classification
    $.validator.addMethod("exactlyOneTaxClass", function() {
        return $('.tax-classification input:checked').length === 1;
    }, "Please select exactly one tax classification");

    // Initialize validation
    $("#w9_form").validate({
        rules: {
            name: "required",
            businessName: {
                required: false
            },
            streetAddress: "required",
            city: "required",
            state: {
                required: true,
                minlength: 2,
                maxlength: 2
            },
            zipCode: {
                required: true,
                digits: true,
                minlength: 5,
                maxlength: 5
            },
            tin: {
                required: true,
                tinFormat: true
            },
            taxClassification: {
                exactlyOneTaxClass: true
            }
        },
        messages: {
            name: "Please enter your full name",
            streetAddress: "Please enter your street address",
            city: "Please enter your city",
            state: {
                required: "Please enter state",
                minlength: "Use 2-letter abbreviation",
                maxlength: "Use 2-letter abbreviation"
            },
            zipCode: {
                required: "Please enter ZIP code",
                digits: "ZIP must be numbers only",
                minlength: "ZIP must be 5 digits",
                maxlength: "ZIP must be 5 digits"
            },
            tin: {
                required: "Please enter your TIN",
                tinFormat: "Invalid format (use XXX-XX-XXXX or XX-XXXXXXX)"
            }
        },
        errorPlacement: function(error, element) {
            if (element.attr("name") === "taxClassification") {
                error.insertAfter(".tax-classification");
            } else {
                error.insertAfter(element);
            }
        },
        submitHandler: function(form) {
            // Handle successful submission
            form.submit();
        }
    });

    // Convert tax classification checkboxes to radio behavior
    $('.tax-classification input[type="checkbox"]').on('change', function() {
        $('.tax-classification input[type="checkbox"]').not(this).prop('checked', false);
    });
});
</script>
</html>