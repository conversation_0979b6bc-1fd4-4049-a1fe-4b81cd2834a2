@extends('backend.layouts.master')

@section('title', 'Email Testing Dashboard')

@section('main-content')
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Email Testing Dashboard</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h5>Available Email Templates</h5>
                <p class="text-muted">Preview and test all email templates in your application.</p>
                
                @foreach($templates as $category => $categoryTemplates)
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="mb-0">{{ $category }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($categoryTemplates as $template => $name)
                            <div class="col-md-6 mb-3">
                                <div class="border rounded p-3">
                                    <h6>{{ $name }}</h6>
                                    <p class="text-muted small">{{ $template }}</p>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ route('email-test.preview', ['template' => $template]) }}" 
                                           class="btn btn-outline-primary" target="_blank">
                                            <i class="fas fa-eye"></i> Preview
                                        </a>
                                        <button type="button" 
                                                class="btn btn-outline-success send-test-btn" 
                                                data-template="{{ $template }}"
                                                data-name="{{ $name }}">
                                            <i class="fas fa-paper-plane"></i> Test
                                        </button>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button type="button" class="btn btn-info btn-block" id="queue-status-btn">
                                <i class="fas fa-list"></i> Check Queue Status
                            </button>
                        </div>
                        <div class="mb-3">
                            <button type="button" class="btn btn-warning btn-block" id="failed-jobs-btn">
                                <i class="fas fa-exclamation-triangle"></i> View Failed Jobs
                            </button>
                        </div>
                        <div class="mb-3">
                            <a href="{{ route('email-test.preview', ['template' => 'backend.emails.order_details_customer']) }}" 
                               class="btn btn-primary btn-block" target="_blank">
                                <i class="fas fa-eye"></i> Preview Sample Order
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">Testing Tips</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success"></i> Use preview to check layout and styling</li>
                            <li><i class="fas fa-check text-success"></i> Send test emails to verify delivery</li>
                            <li><i class="fas fa-check text-success"></i> Test on different email clients</li>
                            <li><i class="fas fa-check text-success"></i> Check mobile responsiveness</li>
                            <li><i class="fas fa-check text-success"></i> Verify all links work correctly</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Send Test Email</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="testEmailForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label>Template</label>
                        <input type="text" class="form-control" id="test-template" readonly>
                    </div>
                    <div class="form-group">
                        <label>Email Address</label>
                        <input type="email" class="form-control" id="test-email" required 
                               placeholder="Enter email address to send test">
                    </div>
                    <div class="form-group">
                        <label>Custom Data (Optional)</label>
                        <textarea class="form-control" id="test-custom-data" rows="4" 
                                  placeholder='{"key": "value"}'></textarea>
                        <small class="form-text text-muted">JSON format for custom template data</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-paper-plane"></i> Send Test Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Queue Status Modal -->
<div class="modal fade" id="queueStatusModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Queue Status</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="queue-status-content">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> Loading...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.border {
    transition: all 0.3s ease;
}
.border:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Send test email
    $('.send-test-btn').click(function() {
        const template = $(this).data('template');
        const name = $(this).data('name');
        
        $('#test-template').val(template);
        $('#testEmailModal .modal-title').text('Send Test Email: ' + name);
        $('#testEmailModal').modal('show');
    });

    // Handle test email form submission
    $('#testEmailForm').submit(function(e) {
        e.preventDefault();
        
        const template = $('#test-template').val();
        const email = $('#test-email').val();
        const customData = $('#test-custom-data').val();
        
        const submitBtn = $(this).find('button[type="submit"]');
        const originalText = submitBtn.html();
        
        submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Sending...').prop('disabled', true);
        
        $.ajax({
            url: '{{ route("email-test.send") }}',
            method: 'POST',
            data: {
                template: template,
                email: email,
                custom_data: customData,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    alert('Test email sent successfully!');
                    $('#testEmailModal').modal('hide');
                    $('#testEmailForm')[0].reset();
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Error: ' + (response ? response.message : 'Unknown error occurred'));
            },
            complete: function() {
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Queue status
    $('#queue-status-btn').click(function() {
        $('#queueStatusModal').modal('show');
        loadQueueStatus();
    });

    function loadQueueStatus() {
        $('#queue-status-content').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading...</div>');
        
        // This would typically call an API endpoint to get queue status
        setTimeout(function() {
            $('#queue-status-content').html(`
                <div class="row">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-primary">Pending Jobs</h5>
                                <h2 class="text-primary">12</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-success">Processed Today</h5>
                                <h2 class="text-success">156</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title text-danger">Failed Jobs</h5>
                                <h2 class="text-danger">3</h2>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <p class="text-muted">Queue workers are running normally. Last processed: 2 minutes ago</p>
                </div>
            `);
        }, 1000);
    }

    // Failed jobs
    $('#failed-jobs-btn').click(function() {
        alert('This would show failed jobs. Implement based on your admin panel structure.');
    });
});
</script>
@endpush
