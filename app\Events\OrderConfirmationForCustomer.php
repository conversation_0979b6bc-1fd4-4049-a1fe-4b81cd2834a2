<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class OrderConfirmationForCustomer
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $showPrices;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Order $order, $showPrices = true)
    {
        $this->order = $order;
        $this->showPrices = $showPrices;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
