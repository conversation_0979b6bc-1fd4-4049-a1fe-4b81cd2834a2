<?php

namespace App\Imports;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;

class CustomerImport implements ToModel, WithChunkReading, WithHeadingRow
{
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        if(!empty($row['billing_address'])){
            $address      = str_replace([" ","%2C"], ["+",","], $row['billing_address']);
            $geocode      = @file_get_contents('https://maps.google.com/maps/api/geocode/json?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&address='.$address.'&sensor=false&libraries=places');
            dd($geocode);
            $json         = json_decode($geocode);
            dd($json);
            if(@$json->{'results'}) {
                dd($json);
                    $billing_long = $json->{'results'}[0]->{'geometry'}->{'location'}->{'lng'};
                    $billing_lat = $json->{'results'}[0]->{'geometry'}->{'location'}->{'lat'};

            }
        }

        if(!empty($row['shipping_address'])){
            $address2      = str_replace([" ","%2C"], ["+",","], $row['shipping_address']);
            $geocode2      = @file_get_contents('https://maps.google.com/maps/api/geocode/json?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&address='.$address.'&sensor=false&libraries=places');
            $json2         = json_decode($geocode2);
            if(@$json2->{'results'}) {
                    $shipping_long = $json->{'results'}[0]->{'geometry'}->{'location'}->{'lng'};
                    $shipping_lat = $json->{'results'}[0]->{'geometry'}->{'location'}->{'lat'};

            }
        }
        $id = null;
        if(!empty($row['salesman_id'])){
            $user = User::whereEmail($row['salesman_id'])->where('role','salesman')->first();
            if($user){
                $id = $user->id;
            }
        }
        return User::updateOrCreate(
            ['email' => $row['email']], // search by email
            [
            'first_name' => $row['first_name'],
            'last_name' => $row['last_name'],
            'password' => Str::random(12),
            'role' => $row['role'],
            'company_name' => $row['company_name'] ?? null,
            'billing_address' => $row['billing_address'] ?? null,
            'billing_lat' => $billing_lat ?? null,
            'billing_long' => $billing_long ?? null,
            'billing_city' => $billing_city ?? null,
            'billing_state' => $billing_state ?? null,
            'billing_zip' => $billing_zip ?? null,
            'shipping_address' => $row['shipping_address'] ?? null,
            'shipping_lat' => $shipping_lat ?? null,
            'shipping_long' => $shipping_long ?? null,
            'shipping_city' => $shipping_city ?? null,
            'shipping_state' => $shipping_state ?? null,
            'shipping_zip' => $shipping_zip ?? null,
            'contact_phone' => $row['contact_phone'] ?? null,
            'account_phone' => $row['account_phone'] ?? null,
            'salesman_id' => $id,
            'is_imported' => true,
            'update_pwd' => true

        ]);
    }

    public function chunkSize(): int
    {
        return 100; // you can change this number as needed
    }
}
