<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->unsignedBigInteger('order_id')->nullable()->after('user_id');
            $table->string('title')->nullable()->after('order_id');
            $table->json('images')->nullable()->after('review');
            $table->text('moderator_notes')->nullable()->after('images');
            $table->softDeletes()->after('updated_at');

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_reviews', function (Blueprint $table) {
            $table->dropForeign(['order_id']);

            $table->dropColumn([
                'order_id',
                'title',
                'images',
                'moderator_notes',
                'deleted_at'
            ]);
        });
    }
};
