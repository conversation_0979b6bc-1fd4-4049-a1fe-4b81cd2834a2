<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use App\Models\User;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Size;
use App\Models\Color;
use App\Models\PriceLevelList;
use App\Models\ItemColor;
use App\Models\ItemSize;
use App\Models\ItemCategory;
use App\Models\ItemPriceLevelList;
use App\Models\AdditionalItemImages;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Storage;
use Picqer\Barcode\BarcodeGeneratorPNG;
use Illuminate\Support\Facades\Validator;

use Illuminate\Support\Str;

class ProductController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $products = Product::with('item_colors', 'sizes')->get();
            return DataTables::of($products)
                ->addIndexColumn()
                ->addColumn('DT_RowIndex', function ($row) {
                    return $row->id;
                })
                ->addColumn('title', function ($row) {
                    return $row->title;
                })
                ->addColumn('is_featured', function ($row) {
                    return ($row->is_featured == 1) ? 'Yes' : 'No';
                })
                ->addColumn('discount', function ($row) {
                    return $row->discount . '% OFF';
                })
                ->addColumn('stock', function ($row) {
                    return $row->total_stock > 0 ? '<span class="badge badge-primary">' . $row->total_stock . '</span>' : '<span class="badge badge-danger">' . $row->total_stock . '</span>';
                })
                ->addColumn('photo', function ($row) {
                    $photo = asset('storage/' . $row->first_item_color_image);
                    return '<img src="' . $photo . '" class="img-fluid zoom" style="max-width:80px" alt="' . $row->first_item_color_image . '">';
                })
                ->addColumn('status', function ($row) {
                    return $row->status == 'active' ? '<span class="badge badge-success">' . $row->status . '</span>' : '<span class="badge badge-warning">' . $row->status . '</span>';
                })
                ->addColumn('size', function ($row) {
                    $dimensions = [];

                    if (!empty($row->actual_width)) {
                        $dimensions[] = $row->actual_width . ' width';
                    }
                    if (!empty($row->actual_depth)) {
                        $dimensions[] = $row->actual_depth . ' depth';
                    }
                    if (!empty($row->actual_height)) {
                        $dimensions[] = $row->actual_height . ' height';
                    }
                    if (!empty($row->actual_length)) {
                        $dimensions[] = $row->actual_length . ' length';
                    }
                    if (!empty($row->actual_diameter)) {
                        $dimensions[] = $row->actual_diameter . ' diameter';
                    }

                    return implode(' x ', $dimensions);
                })
                ->addColumn('action', function ($row) {
                    $editBtn = '<a href="' . route('product.edit', $row->id) . '" class="btn btn-primary btn-sm mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="edit" data-placement="bottom"><i class="fas fa-edit"></i></a>';
                    $deleteBtn = '<form method="POST" action="' . route('product.destroy', $row->id) . '">' .
                        csrf_field() .
                        method_field('DELETE') .
                        '<button class="btn btn-danger btn-sm dltBtn" data-id="' . $row->id . '" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" data-placement="bottom" title="Delete"><i class="fas fa-trash-alt"></i></button>' .
                        '</form>';
                    return $editBtn . $deleteBtn;
                })
                ->rawColumns(['category', 'stock', 'photo', 'status', 'action'])
                ->make(true);
        }

        return view('backend.product.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $brands = Brand::get();
        $categories = Category::where([['is_parent', 1], ['type', 'item'], ['status', 'active']])->get();
        $sizes = Size::where('status', 'active')->get();
        $colors = Color::where('status', 'active')->get();
        $price_level_lists = PriceLevelList::where('status', 'active')->get();
        $parent_cats = Category::where('is_parent', 1)->orderBy('title', 'ASC')->get();
        $products = Product::all();
        $users = User::all();

        return view('backend.product.create', compact('products','users','categories', 'brands','sizes','colors','price_level_lists','parent_cats'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'summary' => 'required|string',
                'description' => 'nullable|string',
                'discount' => 'nullable|numeric|min:0|max:100',
                'items_per_box' => 'nullable|numeric',
                'weight' => 'required|numeric',
                'shipping_width' => 'nullable|numeric|min:0',
                'shipping_depth' => 'nullable|numeric|min:0',
                'shipping_height' => 'nullable|numeric|min:0',
                'actual_width' => 'nullable|numeric|min:0',
                'actual_depth' => 'nullable|numeric|min:0',
                'actual_height' => 'nullable|numeric|min:0',
                'actual_length' => 'nullable|numeric|min:0',
                'actual_diameter' => 'nullable|numeric|min:0',
                'cat_id' => 'required|array|min:1',
                'cat_id.*' => 'exists:categories,id',
                'brand_id' => 'required|exists:brands,id',
                'color' => 'required|array',
                'color.*' => 'exists:colors,id',
                'color_item_number' => 'required|array',
                'color_item_number.*' => 'required|string|max:255',
                'color_price' => 'required|array',
                'color_price.*' => 'required|numeric|min:0',
                'color_stock' => 'required|array',
                'color_stock.*' => 'required|numeric|min:0',
                'color_barcode' => 'nullable|array',
                'color_barcode.*' => 'nullable|string|max:255',
                'color_photo' => 'required|array',
                'color_photo.*' => 'required|file|mimes:jpg,png,gif|max:2048',
                'color_additional_images' => 'nullable|array',
                'color_additional_images.*.*' => 'nullable|file|mimes:jpg,png,gif|max:2048',
                'status' => 'required|in:active,inactive',
                'price_level_lists' => 'nullable|array',
                'price_level_lists.*' => 'nullable|exists:price_level_lists,id',
                'weight_unit' => 'required|in:lbs,oz'
            ]);

            // Custom validation for at least one shipping size
            $shippingSizes = array_filter([
                $request->shipping_width,
                $request->shipping_depth,
                $request->shipping_height
            ]);
            if (empty($shippingSizes)) {
                $validator->errors()->add('shipping_size', 'At least one shipping size field is required.');
            }

            $actualSizes = array_filter([
                $request->actual_width,
                $request->actual_depth,
                $request->actual_height,
                $request->actual_length,
                $request->actual_diameter
            ]);
            if (empty($actualSizes)) {
                $validator->errors()->add('actual_size', 'At least one actual size field is required.');
            }

            if ($validator->fails()) {
                return redirect()
                    ->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $slug = generateUniqueSlug($request->title, Product::class);
            $product = Product::create([
                'title' => $request->title,
                'summary' => $request->summary,
                'description' => $request->description,
                'discount' => $request->discount ?? 0,
                'items_per_box' => $request->items_per_box,
                'weight' => $request->weight,
                // 'shipping_width' => $request->shipping_width,
                // 'shipping_depth' => $request->shipping_depth,
                // 'shipping_height' => $request->shipping_height,
                // 'actual_width' => $request->actual_width,
                // 'actual_depth' => $request->actual_depth,
                // 'actual_height' => $request->actual_height,
                // 'actual_length' => $request->actual_length,
                // 'actual_diameter' => $request->actual_diameter,
                'slug' => $slug,
                'is_featured' => $request->input('is_featured', 0),
                'status' => $request->status,
                'brand_id' => $request->brand_id,
                'weight_unit' => $request->weight_unit
            ]);

            if ($request->cat_id) {
                foreach ($request->cat_id as $categoryId) {
                    ItemCategory::create([
                        'product_id' => $product->id,
                        'category_id' => $categoryId,
                    ]);
                }
            }

            if ($request->color) {
                foreach ($request->color as $colorId) {
                    $colorData = [
                        'product_id' => $product->id,
                        'color_id' => $colorId,
                        'price' => $request->color_price[$colorId],
                        'item_number' => $request->color_item_number[$colorId],
                        'stock' => $request->color_stock[$colorId],
                        'barcode_number' => $request->color_barcode[$colorId],
                    ];

                    if ($request->color_barcode[$colorId]) {
                        $itemNumber = $request->color_barcode[$colorId];
                        $path = $this->generateBarcode($itemNumber);
                        $colorData['barcode'] = $path;
                    }

                    if ($request->hasFile("color_photo.$colorId")) {
                        $colorData['photo'] = $request->file("color_photo.$colorId")->store('photos', 'public');
                    }

                    $colorItem = ItemColor::create($colorData);

                    if ($request->hasFile("color_additional_images.$colorId")) {
                        foreach ($request->file("color_additional_images.$colorId") as $image) {
                            $imagePath = $image->store('additional_images', 'public');
                            AdditionalItemImages::create([
                                'product_id' => $product->id,
                                'item_color_id' => $colorId,
                                'image' => $imagePath,
                            ]);
                        }
                    }
                }
            }

            if ($request->price_level_lists) {
                foreach ($request->price_level_lists as $priceLevelId) {
                    ItemPriceLevelList::create([
                        'product_id' => $product->id,
                        'price_level_list_id' => $priceLevelId,
                    ]);
                }
            }

            $message = $product
                ? 'Product Successfully added'
                : 'Please try again!!';

            return redirect()->route('product.index')->with(
                $product ? 'success' : 'error',
                $message
            );
        } catch (\Exception $e) {
            dd($e);
            return back()->with('error', 'Something went wrong');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Implement if needed
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $brands = Brand::where('status', 'active')->get();
        $product = Product::findOrFail($id);
        $categories = Category::where([['is_parent', 1], ['type', 'item'], ['status', 'active']])->get();
        $sizes = Size::where('status', 'active')->get();
        $colors = Color::where('status', 'active')->get();
        $price_level_lists = PriceLevelList::where('status', 'active')->get();
        $item_categories = ItemCategory::where('product_id', $product->id)->pluck('category_id')->toArray();
        $item_sizes = ItemSize::where('product_id', $product->id)->pluck('size_id')->toArray();
        $item_colors = ItemColor::where('product_id', $product->id)->pluck('color_id')->toArray();
        $item_price_level_lists = ItemPriceLevelList::where('product_id', $product->id)->pluck('price_level_list_id')->toArray();
        $item_colors_data = ItemColor::where('product_id', $product->id)->with(['color'])->get();
        $parent_cats = Category::where('is_parent', 1)->orderBy('title', 'ASC')->get();
        $products = Product::all();
        $users = User::all();

        return view('backend.product.edit', compact(
            'product',
            'brands',
            'categories',
            'sizes',
            'colors',
            'price_level_lists',
            'item_categories',
            'item_sizes',
            'item_colors',
            'item_price_level_lists',
            'item_colors_data',
            'parent_cats',
            'products',
            'users'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'summary' => 'required|string',
            'description' => 'nullable|string',
            'discount' => 'nullable|numeric|min:0|max:100',
            'items_per_box' => 'nullable|numeric',
            'weight' => 'nullable|numeric',
            'shipping_width' => 'nullable|numeric|min:0',
            'shipping_depth' => 'nullable|numeric|min:0',
            'shipping_height' => 'nullable|numeric|min:0',
            'actual_width' => 'nullable|numeric|min:0',
            'actual_depth' => 'nullable|numeric|min:0',
            'actual_height' => 'nullable|numeric|min:0',
            'actual_length' => 'nullable|numeric|min:0',
            'actual_diameter' => 'nullable|numeric|min:0',
            'cat_id' => 'required|array|min:1',
            'cat_id.*' => 'exists:categories,id',
            'brand_id' => 'required|exists:brands,id',
            'color' => 'required|array',
            'color.*' => 'exists:colors,id',
            'color_item_number' => 'required|array',
            'color_item_number.*' => 'required|string|max:255',
            'color_price' => 'required|array',
            'color_price.*' => 'required|numeric|min:0',
            'color_stock' => 'required|array',
            'color_stock.*' => 'required|numeric|min:0',
            'color_barcode' => 'nullable|array',
            'color_barcode.*' => 'nullable|string|max:255',
            'color_photo' => 'nullable|array',
            'color_photo.*' => 'nullable|file|mimes:jpg,png,gif|max:2048',
            'color_additional_images' => 'nullable|array',
            'color_additional_images.*.*' => 'nullable|file|mimes:jpg,png,gif|max:2048',
            'status' => 'required|in:active,inactive',
            'price_level_lists' => 'nullable|array',
            'price_level_lists.*' => 'nullable|exists:price_level_lists,id',
            'weight_unit' => 'required|in:lbs,oz'
        ]);

        $shippingSizes = array_filter([
            $request->shipping_width,
            $request->shipping_depth,
            $request->shipping_height
        ]);
        if (empty($shippingSizes)) {
            $validator->errors()->add('shipping_size', 'At least one shipping size field is required.');
        }

        $actualSizes = array_filter([
            $request->actual_width,
            $request->actual_depth,
            $request->actual_height,
            $request->actual_length,
            $request->actual_diameter
        ]);
        if (empty($actualSizes)) {
            $validator->errors()->add('actual_size', 'At least one actual size field is required.');
        }

        if ($validator->fails()) {
            return redirect()
                ->back()
                ->withErrors($validator)
                ->withInput();
        }

        $product = Product::findOrFail($id);
        $currentColorIds = $product->item_colors->pluck('color_id')->toArray();
        $newColorIds = $request->color ?: [];
        $removedColors = array_diff($currentColorIds, $newColorIds);

        $status = $product->update([
            'title' => $request->title,
            'summary' => $request->summary,
            'description' => $request->description,
            'discount' => $request->discount ?? 0,
            'items_per_box' => $request->items_per_box,
            'weight' => $request->weight,
            'shipping_width' => $request->shipping_width,
            'shipping_depth' => $request->shipping_depth,
            'shipping_height' => $request->shipping_height,
            'actual_width' => $request->actual_width,
            'actual_depth' => $request->actual_depth,
            'actual_height' => $request->actual_height,
            'actual_length' => $request->actual_length,
            'actual_diameter' => $request->actual_diameter,
            'is_featured' => $request->input('is_featured', 0),
            'status' => $request->status,
            'brand_id' => $request->brand_id,
            'weight_unit' => $request->weight_unit
        ]);

        if ($request->cat_id) {
            ItemCategory::where('product_id', $product->id)->delete();
            foreach ($request->cat_id as $categoryId) {
                ItemCategory::create([
                    'product_id' => $product->id,
                    'category_id' => $categoryId,
                ]);
            }
        }

        if (!empty($removedColors)) {
            foreach ($removedColors as $colorId) {
                $itemColor = $product->item_colors()->where('color_id', $colorId)->first();
                if ($itemColor) {
                    if ($itemColor->barcode) {
                        Storage::disk('public')->delete($itemColor->barcode);
                    }
                    if ($itemColor->photo) {
                        Storage::disk('public')->delete($itemColor->photo);
                    }
                    AdditionalItemImages::where('product_id', $product->id)
                        ->where('item_color_id', $colorId)
                        ->get()
                        ->each(function ($image) {
                            Storage::disk('public')->delete($image->image);
                            $image->delete();
                        });
                    $itemColor->delete();
                }
            }
        }

        if ($request->color) {
            foreach ($request->color as $colorId) {
                $colorData = [
                    'price' => $request->color_price[$colorId],
                    'item_number' => $request->color_item_number[$colorId],
                    'stock' => $request->color_stock[$colorId],
                    'barcode_number' => $request->color_barcode[$colorId]
                ];

                if ($request->color_barcode[$colorId]) {
                    $existingItemColor = $product->item_colors()->where('color_id', $colorId)->first();
                    if ($existingItemColor && $existingItemColor->barcode) {
                        Storage::disk('public')->delete($existingItemColor->barcode);
                    }
                    $itemNumber = $request->color_barcode[$colorId];
                    $path = $this->generateBarcode($itemNumber);
                    $colorData['barcode'] = $path;
                }

                if ($request->hasFile("color_photo.$colorId")) {
                    $existingItemColor = $product->item_colors()->where('color_id', $colorId)->first();
                    if ($existingItemColor && $existingItemColor->photo) {
                        Storage::disk('public')->delete($existingItemColor->photo);
                    }
                    $colorData['photo'] = $request->file("color_photo.$colorId")->store('photos', 'public');
                }

                $product->item_colors()->updateOrCreate(
                    ['color_id' => $colorId, 'product_id' => $product->id],
                    $colorData
                );

                if ($request->hasFile("color_additional_images.$colorId")) {
                    AdditionalItemImages::where('product_id', $product->id)
                        ->where('item_color_id', $colorId)
                        ->get()
                        ->each(function ($image) {
                            Storage::disk('public')->delete($image->image);
                            $image->delete();
                        });

                    foreach ($request->file("color_additional_images.$colorId") as $image) {
                        $imagePath = $image->store('additional_images', 'public');
                        AdditionalItemImages::create([
                            'product_id' => $product->id,
                            'item_color_id' => $colorId,
                            'image' => $imagePath,
                        ]);
                    }
                }
            }
        }

        if ($request->price_level_lists) {
            ItemPriceLevelList::where('product_id', $product->id)->delete();
            foreach ($request->price_level_lists as $priceLevelId) {
                ItemPriceLevelList::create([
                    'product_id' => $product->id,
                    'price_level_list_id' => $priceLevelId,
                ]);
            }
        }

        $message = $status
            ? 'Product Successfully updated'
            : 'Please try again!!';

        return redirect()->route('product.index')->with(
            $status ? 'success' : 'error',
            $message
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $product = Product::findOrFail($id);
        $status = $product->delete();

        $message = $status
            ? 'Product successfully deleted'
            : 'Error while deleting product';

        return redirect()->route('product.index')->with(
            $status ? 'success' : 'error',
            $message
        );
    }

    public function generateBarcode($id)
    {
        $itemNumber = str_pad(substr((string)$id, 0, 11), 11, '0', STR_PAD_LEFT);

        $sum = 0;
        for ($i = 0; $i < 11; $i++) {
            $digit = (int)$itemNumber[$i];
            $sum += ($i % 2 === 0) ? $digit * 3 : $digit;
        }
        $mod = $sum % 10;
        $checkDigit = $mod === 0 ? 0 : 10 - $mod;

        $upcA = $itemNumber . (string)$checkDigit;

        $generator = new BarcodeGeneratorPNG();
        $barcode = $generator->getBarcode($upcA, $generator::TYPE_UPC_A);
        $path = "barcodes/{$upcA}.png";
        Storage::disk('public')->put($path, $barcode);

        $fullPath = Storage::disk('public')->path($path);

        return $path;
    }
}
