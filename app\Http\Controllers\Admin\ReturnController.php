<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ReturnRequest;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\EmailController;

class ReturnController extends Controller
{
    public function store(Request $request, EmailController $email_controller)
    {
        // dd($request->all());
        $data = $request->validate([
            'order_id' => 'required|exists:orders,id',
            'return_type' => 'required|in:full,partial',
            'items' => 'required_if:return_type,partial|array',
            'items.*.product_id' => 'required_if:return_type,partial|exists:products,id',
            'items.*.color_id' => 'required_if:return_type,partial|exists:colors,id',
            'items.*.return_quantity' => 'nullable|numeric|min:1',
            'return_reason' => 'required|string|max:255',
        ]);

        if ($data['return_type'] === 'partial') {
            foreach ($data['items'] as $index => $item) {
                if (isset($item['selected']) && $item['selected'] === 'on') {
                    $rules["items.$index.return_quantity"][] = 'required';
                }
            }
        }

        try {
            DB::beginTransaction();

            $order = Order::findOrFail($data['order_id']);
            $returns = [];

            if ($data['return_type'] === 'full') {
                $order->items()->each(function ($item) use ($data, $order) {
                    ReturnRequest::create([
                        'order_id' => $order->id,
                        'order_item_id' => $item->id,
                        'product_id' => $item->product_id,
                        'color_id' => $item->color,
                        'return_quantity' => $item->quantity,
                        'reason' => $data['return_reason'],
                        'status' => 'requested',
                    ]);

                    $item->delete();
                });

                $order->update([
                    'return_status' => 'requested',
                    'total' => 0,
                ]);

                $returns = $order->items();
            } else {
                foreach ($request->items as $item) {
                    if (!isset($item['selected']) || $item['selected'] !== 'on') {
                        continue;
                    }

                    $orderItem = OrderItem::where('order_id', $order->id)
                        ->where('product_id', $item['product_id'])
                        ->where('color', $item['color_id'])
                        ->firstOrFail();

                    if ($item['return_quantity'] > $orderItem->quantity) {
                        throw ValidationException::withMessages([
                            'items' => 'Return quantity exceeds ordered quantity',
                        ]);
                    }

                    ReturnRequest::create([
                        'order_id' => $order->id,
                        'order_item_id' => $orderItem->id,
                        'product_id' => $item['product_id'],
                        'color_id' => $item['color_id'],
                        'return_quantity' => $item['return_quantity'],
                        'reason' => $data['return_reason'],
                        'status' => 'requested',
                    ]);

                    $updatedQuantity = $orderItem->quantity - $item['return_quantity'];

                    if ($updatedQuantity === 0) {
                        $orderItem->delete();
                    } else {
                        $orderItem->update([
                            'is_returned' => true,
                            'quantity' => $updatedQuantity,
                            'total' => $orderItem->price * $updatedQuantity,
                        ]);
                    }
                }

                $order->update([
                    'return_status' => 'requested',
                    'total' => $order->items()->sum('total'),
                ]);

                $returns = $request->items;
            }

            $email_controller->send_return($order, $returns, $data['return_reason']);

            DB::commit();

            return redirect()->route('salesman.orders.edit', $order->id)->with('success', 'Return request submitted successfully');
        } catch(\Exception $e){
            DB::rollBack();
            dd($e);
            request()->session()->flash('error', 'Error creating order: ' . $e->getMessage());
            return back()->withInput();
        }
    }
}
