<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Permission;
use Illuminate\Support\Str;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Seed Permissions
        $permissionItems = [
            [
                'name'        => 'Can View Users',
                'slug'        => 'view.users',
                'description' => 'Can view users',
                'model'       => 'Permission',
            ],
            [
                'name'        => 'Can Create Users',
                'slug'        => 'create.users',
                'description' => 'Can create new users',
                'model'       => 'Permission',
            ],
            [
                'name'        => 'Can Edit Users',
                'slug'        => 'edit.users',
                'description' => 'Can edit users',
                'model'       => 'Permission',
            ],
            [
                'name'        => 'Can Delete Users',
                'slug'        => 'delete.users',
                'description' => 'Can delete users',
                'model'       => 'Permission',
            ],
        ];

        foreach ($permissionItems as $permissionItem) {
            Permission::firstOrCreate([
                'slug' => $permissionItem['slug'],
            ], $permissionItem);
        }

        if (User::where('email', '=', '<EMAIL>')->first() === null) {
            $adminUser = User::create([
                'first_name'              => 'Admin',
                'last_name'               => 'Admin',
                'email'                   => '<EMAIL>',
                'password'                => Hash::make('password'),
                'role'                    => 'admin',
                'status'                  => 'active',
                'email_verified_at'       => now(),
                'photo'                   => null,
                'provider'                => null,
                'provider_id'             => null,
                'remember_token'          => Str::random(10),
                'company_name'            => 'Admin Company',
                'billing_address'         => '123 Admin St',
                'billing_lat'             => '37.7749',
                'billing_long'            => '-122.4194',
                'billing_city'            => 'Admin City',
                'billing_state'           => 'CA',
                'billing_zip'             => '90001',
                'shipping_address'        => '123 Admin St',
                'shipping_lat'            => '37.7749',
                'shipping_long'           => '-122.4194',
                'shipping_city'           => 'Admin City',
                'shipping_state'          => 'CA',
                'shipping_zip'            => '90001',
                'contact_phone'           => '************',
                'account_phone'           => '************',
                'w9_name'                 => 'Admin W9 Name',
                'w9_business_name'        => 'Admin Biz',
                'w9_tax_classification'   => 'C Corporation',
                'w9_street_address'       => '123 Admin Tax Rd',
                'w9_city'                 => 'Admin City',
                'w9_state'                => 'CA',
                'w9_zip_code'             => '90001',
                'w9_tin'                  => '12-3456789',
                'salesman_id'            => null,
            ]);
        
            // Assign all permissions to admin
            $permissions = Permission::all();
            $adminUser->permissions()->sync($permissions->pluck('id'));
        }
        
        if (User::where('email', '=', '<EMAIL>')->first() === null) {
            $regularUser = User::create([
                'first_name'              => 'User',
                'last_name'               => 'User',
                'email'                   => '<EMAIL>',
                'password'                => Hash::make('password'),
                'role'                    => 'user',
                'status'                  => 'active',
                'email_verified_at'       => now(),
                'photo'                   => null,
                'provider'                => null,
                'provider_id'             => null,
                'remember_token'          => Str::random(10),
                'company_name'            => 'User Company',
                'billing_address'         => '456 User Ave',
                'billing_lat'             => '34.0522',
                'billing_long'            => '-118.2437',
                'billing_city'            => 'User City',
                'billing_state'           => 'CA',
                'billing_zip'             => '90002',
                'shipping_address'        => '456 User Ave',
                'shipping_lat'            => '34.0522',
                'shipping_long'           => '-118.2437',
                'shipping_city'           => 'User City',
                'shipping_state'          => 'CA',
                'shipping_zip'            => '90002',
                'contact_phone'           => '************',
                'account_phone'           => '************',
                'w9_name'                 => 'User W9 Name',
                'w9_business_name'        => 'User Biz',
                'w9_tax_classification'   => 'Sole Proprietor',
                'w9_street_address'       => '456 User Tax Blvd',
                'w9_city'                 => 'User City',
                'w9_state'                => 'CA',
                'w9_zip_code'             => '90002',
                'w9_tin'                  => '98-7654321',
                'salesman_id'            => null,
            ]);
        }
    }
}
