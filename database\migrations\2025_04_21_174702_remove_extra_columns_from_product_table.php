<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn('item_number');
            $table->dropColumn('price');
            $table->dropColumn('barcode');
            $table->dropColumn('photo');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->string('item_number')->unique()->nullable();
            $table->decimal('price', 10, 2);
            $table->string('barcode')->unique()->nullable();
            $table->string('photo')->nullable();
        });
    }
};
