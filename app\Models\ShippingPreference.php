<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShippingPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'preferred_method', 'carrier_preference',
        'delivery_window_start', 'delivery_window_end', 'is_default'
    ];

    protected $casts = [
        'is_default' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
