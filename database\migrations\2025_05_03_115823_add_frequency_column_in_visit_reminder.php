<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visit_reminders', function (Blueprint $table) {
            $table->enum('frequency', ['day', 'week', 'month', 'year'])->nullable();
            $table->integer('interval')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visit_reminders', function (Blueprint $table) {
            $table->dropColumn('frequency');
            $table->dropColumn('interval');
        });
    }
};
