<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests" isTestSource="true" packagePrefix="Tests\" />
      <sourceFolder url="file://$MODULE_DIR$/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/tests/Unit" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/tests/Feature" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/packages/laravel-filterable/src" isTestSource="false" packagePrefix="Touhidurabir\Filterable\" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/asm89/stack-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/barryvdh/laravel-dompdf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dnoegel/php-xdg-base-dir" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/instantiator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dompdf/dompdf" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/drewm/mailchimp-api" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/facade/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/facade/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/facade/ignition-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fideloper/proxy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fruitcake/laravel-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fzaninotto/faker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/intervention/image" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/socialite" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/ui" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/oauth1-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/opis/closure" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/random_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/paragonie/sodium_compat" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phenx/php-font-lib" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phenx/php-svg-lib" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpspec/prophecy" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-token-stream" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/pusher/pusher-php-server" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sabberworm/php-css-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/scrivo/highlight.php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/resource-operations" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/laravel-newsletter" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/srmklive/paypal" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/swiftmailer/swiftmailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-iconv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php70" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php73" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/unisharp/laravel-filemanager" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/webmozart/assert" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/backtrace" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/ignition" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/flare-client-php" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/laravel-ignition" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/masterminds/html5" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/spatie/laravel-package-tools" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>