<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Size;

class SizeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Size::select('id', 'name', 'status')->get();
            return DataTables::of($data)
                ->addIndexColumn()
                ->addColumn('name', function ($row) {
                    return $row->name;
                })
                ->addColumn('status', function ($row) {
                    if ($row->status == 'active') {
                        return '<span class="badge badge-success">Active</span>';
                    } else {
                        return '<span class="badge badge-warning">Inactive</span>';
                    }
                })
                ->addColumn('action', function ($row) {
                    $editBtn = '<a href="' . route('size.edit', $row->id) . '" class="btn btn-primary btn-sm mr-1" data-toggle="tooltip" title="Edit"><i class="fas fa-edit"></i></a>';
                    $deleteBtn = '<form method="POST" action="' . route('size.destroy', $row->id) . '" style="display:inline">
                                    ' . csrf_field() . '
                                    ' . method_field('DELETE') . '
                                    <button type="submit" class="btn btn-danger btn-sm dltBtn" data-toggle="tooltip" title="Delete"><i class="fas fa-trash-alt"></i></button>
                                </form>';
                    return $editBtn . $deleteBtn;
                })
                ->rawColumns(['status', 'action'])
                ->make(true);
        }

        return view('backend.size.index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('backend.size.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string',
            'status' => 'required|in:active,inactive',
        ]);

        $size = new Size;
        $size->name = $validatedData['name'];
        $size->status = $validatedData['status'];
        $size->save();

        $message = $size
            ? 'Size Successfully added'
            : 'Please try again!!';

         if ($request->ajax()) {
            return response()->json([
                'id' => $size->id,
                'name' => $size->name
            ]);
        }   
        return redirect()->route('size.index')->with(
            $size ? 'success' : 'error',
            $message
        );
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $size = Size::findOrFail($id);

        return view('backend.size.edit', compact('size'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // dd($request->all());
        $validatedData = $request->validate([
            'name' => 'required|string',
            'status' => 'required|in:active,inactive',
        ]);
        $size = Size::find($id);
        $size->name = $validatedData['name'];
        $size->status = $validatedData['status'];
        $size = $size->update();

        $message = $size
            ? 'Size Successfully Updated'
            : 'Please try again!!';

        return redirect()->route('size.index')->with(
            $size ? 'success' : 'error',
            $message
        );
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $size = Size::findOrFail($id);
        $status = $size->delete();

        $message = $status
            ? 'Size successfully deleted'
            : 'Error while deleting size';

        return redirect()->route('size.index')->with(
            $status ? 'success' : 'error',
            $message
        );
    }
}
