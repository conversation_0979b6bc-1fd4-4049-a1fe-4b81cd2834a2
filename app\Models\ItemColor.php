<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ItemColor extends Model
{
    use HasFactory;

    protected $table = 'item_colors';

    protected $fillable = ['product_id','color_id', 'product_number', 'barcode', 'price', 'photo', 'item_number', 'stock', 'barcode_number'];

    protected $appends = ['additional_images'];

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function color()
    {
        return $this->belongsTo(Color::class, 'color_id');
    }

    public function getAdditionalImagesAttribute()
    {
        return AdditionalItemImages::where('item_color_id', $this->color_id)->where('product_id', $this->product_id)->get();
    }

    public function orderPrices()
    {
        return $this->hasMany(OrderPrice::class);
    }

    public function userPrices()
    {
        return $this->hasMany(UserItemColorPrice::class);
    }

    public function getPriceForUserAttribute($userId)
    {
        $userPrice = $this->userPrices()->where('user_id', $userId)->where('item_color_id', $this->id)->first();
        return [
            'original_price' => $this->price,
            'custom_price' => $userPrice ? $userPrice->custom_price : $this->price,
        ];
    }
}
