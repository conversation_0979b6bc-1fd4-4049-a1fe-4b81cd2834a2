<?php

namespace App\Listeners;

use App\Events\OrderStatusChanged;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderStatusChangedEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  OrderStatusChanged  $event
     * @return void
     */
    public function handle(OrderStatusChanged $event)
    {
        $order = $event->order;
        $oldStatus = $event->oldStatus;
        $newStatus = $event->newStatus;

        // Send email to customer about status change
        $customerData = [
            'order' => $order,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
        ];

        Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
            ->queue(new MailQueue(
                'backend.emails.order_status_changed_customer',
                $customerData,
                'Order Status Update - #' . $order->order_number
            ));

        // Send notification to salesman if assigned
        if ($order->salesman_id) {
            $salesman = $order->salesman;
            if ($salesman) {
                // Note: This template would need to be created if salesman notifications are needed
                // Mail::to($salesman->email, $salesman->first_name . ' ' . $salesman->last_name)
                //     ->queue(new MailQueue(
                //         'backend.emails.order_status_changed_salesman',
                //         $customerData,
                //         'Order Status Changed - #' . $order->order_number
                //     ));
            }
        }

        // Special handling for specific status changes
        switch ($newStatus) {
            case 'shipped':
                $this->handleShippedStatus($order);
                break;
            case 'delivered':
                $this->handleDeliveredStatus($order);
                break;
            case 'cancelled':
                $this->handleCancelledStatus($order);
                break;
        }
    }

    /**
     * Handle shipped status
     */
    private function handleShippedStatus($order)
    {
        $data = ['order' => $order];

        // Note: These templates would need to be created for specific status notifications
        // Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
        //     ->queue(new MailQueue(
        //         'backend.emails.order_shipped_customer',
        //         $data,
        //         'Your Order Has Shipped - #' . $order->order_number
        //     ));
    }

    /**
     * Handle delivered status
     */
    private function handleDeliveredStatus($order)
    {
        $data = ['order' => $order];

        // Note: These templates would need to be created for specific status notifications
        // Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
        //     ->queue(new MailQueue(
        //         'backend.emails.order_delivered_customer',
        //         $data,
        //         'Order Delivered - Please Review - #' . $order->order_number
        //     ));
    }

    /**
     * Handle cancelled status
     */
    private function handleCancelledStatus($order)
    {
        $data = ['order' => $order];

        // Note: These templates would need to be created for specific status notifications
        // Mail::to($order->email, $order->first_name . ' ' . $order->last_name)
        //     ->queue(new MailQueue(
        //         'backend.emails.order_cancelled_customer',
        //         $data,
        //         'Order Cancelled - #' . $order->order_number
        //     ));
    }
}
