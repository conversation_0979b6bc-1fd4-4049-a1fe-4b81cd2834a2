@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Edit User</h5>
    <div class="card-body">
        <form method="post" action="{{ route('users.update', $user->id) }}" id="userForm">
            @csrf
            @method('PATCH')

            <!-- Personal Information Section -->
            <div class="form-section">
                <h3>Personal Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="col-form-label">First Name<span class="text-danger">*</span></label>
                            <input id="first_name" type="text" name="first_name" placeholder="Enter first name" value="{{ old('first_name', $user->first_name) }}" class="form-control" data-required="true" data-maxlength="255">
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="col-form-label">Last Name<span class="text-danger">*</span></label>
                            <input id="last_name" type="text" name="last_name" placeholder="Enter last name" value="{{ old('last_name', $user->last_name) }}" class="form-control" data-required="true" data-maxlength="255">
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="col-form-label">Email<span class="text-danger">*</span></label>
                            <input id="email" type="email" name="email" placeholder="Enter email" value="{{ old('email', $user->email) }}" class="form-control" data-required="true" data-email="true" readonly>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role" class="col-form-label">Role<span class="text-danger">*</span></label>
                            <select name="role" id="role" class="form-control" data-required="{{ Auth::user()->role == 'admin' ? 'true' : 'false' }}" data-allowed="admin,user,customer,salesman,office,picker">
                                <option value="">-- Select Role --</option>
                                @foreach($userRoles as $role)
                                    <option value="{{ $role }}" {{ old('role', $user->role) === $role ? 'selected' : '' }}>{{ ucfirst($role) }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6 salesman" style="display: none;">
                        <div class="form-group">
                            <label for="salesman_id" class="col-form-label">Salesman<span class="text-danger">*</span></label>
                            <select name="salesman_id" id="salesman_id" class="form-control select2" data-conditional-required="role:customer,user">
                                <option value="">-- Select Salesman --</option>
                                <option value="new"><strong>Create New Salesman</strong></option>
                                @foreach($salesmans as $salesman)
                                    <option value="{{ $salesman->id }}" {{ old('salesman_id', $user->salesman_id) == $salesman->id ? 'selected' : '' }}>{{ $salesman->first_name . ' ' . $salesman->last_name }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div id="user_details" style="display: none;">
                <!-- Company Information Section -->
                <div class="form-section">
                    <h3>Company Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="company_name" class="col-form-label">Company Name<span class="text-danger">*</span></label>
                                <input id="company_name" type="text" name="company_name" placeholder="Enter company name" value="{{ old('company_name', $user->company_name) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Billing Address Section -->
                <div class="form-section">
                    <h3>Billing Address</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="billing_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                                <input id="billing_address" type="text" name="billing_address" placeholder="Enter billing address" value="{{ old('billing_address', $user->billing_address) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <input type="hidden" id="billing_lat" name="billing_lat" value="{{ old('billing_lat', $user->billing_lat) }}">
                                <input type="hidden" id="billing_long" name="billing_long" value="{{ old('billing_long', $user->billing_long) }}">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="billing_city" class="col-form-label">City<span class="text-danger">*</span></label>
                                <input id="billing_city" type="text" name="billing_city" placeholder="Enter city" value="{{ old('billing_city', $user->billing_city) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="billing_state" class="col-form-label">State<span class="text-danger">*</span></label>
                                <input id="billing_state" type="text" name="billing_state" placeholder="Enter state" value="{{ old('billing_state', $user->billing_state) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="billing_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                                <input id="billing_zip" type="text" name="billing_zip" placeholder="Enter ZIP code" value="{{ old('billing_zip', $user->billing_zip) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="10">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipping Address Section -->
                <div class="form-section">
                    <h3>Shipping Address</h3>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="same_as_billing" name="same_as_billing"> Check here if shipping address is the same as billing address
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="shipping_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                                <input id="shipping_address" type="text" name="shipping_address" placeholder="Enter shipping address" value="{{ old('shipping_address', $user->shipping_address) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <input type="hidden" id="shipping_lat" name="shipping_lat" value="{{ old('shipping_lat', $user->shipping_lat) }}">
                                <input type="hidden" id="shipping_long" name="shipping_long" value="{{ old('shipping_long', $user->shipping_long) }}">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="shipping_city" class="col-form-label">City<span class="text-danger">*</span></label>
                                <input id="shipping_city" type="text" name="shipping_city" placeholder="Enter city" value="{{ old('shipping_city', $user->shipping_city) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="shipping_state" class="col-form-label">State<span class="text-danger">*</span></label>
                                <input id="shipping_state" type="text" name="shipping_state" placeholder="Enter state" value="{{ old('shipping_state', $user->shipping_state) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="shipping_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                                <input id="shipping_zip" type="text" name="shipping_zip" placeholder="Enter ZIP code" value="{{ old('shipping_zip', $user->shipping_zip) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="10">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information Section -->
                <div class="form-section">
                    <h3>Contact Information</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="contact_phone" class="col-form-label">Contact Phone Number<span class="text-danger">*</span></label>
                                <input id="contact_phone" type="text" name="contact_phone" placeholder="Enter contact phone number" value="{{ old('contact_phone', $user->contact_phone) }}" class="form-control" data-conditional-required="role:not:admin,office,salesman" data-maxlength="20">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="account_phone" class="col-form-label">Account Payable Phone Number</label>
                                <input id="account_phone" type="text" name="account_phone" placeholder="Enter account payable phone number" value="{{ old('account_phone', $user->account_phone) }}" class="form-control" data-maxlength="20">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Connection Section -->
                <div class="form-section">
                    <h3>Connections</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="price_level_lists">Price Level Lists<span class="text-danger">*</span></label>
                                <select name="price_level_lists[]" id="price_level_lists" class="form-control select2" multiple data-conditional-required="role:not:admin,office,salesman" data-min="1">
                                    @foreach($price_level_lists as $price_level_list)
                                        <option value="{{ $price_level_list->id }}"
                                            {{ in_array($price_level_list->id, old('price_level_lists', $user_price_level_lists ?? [])) ? 'selected' : '' }}>
                                            {{ $price_level_list->title }}
                                        </option>
                                    @endforeach
                                </select>
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="cat_id">Category<span class="text-danger">*</span></label>
                                <select name="cat_id[]" id="cat_id" class="form-control select2" multiple data-conditional-required="role:not:admin,office,salesman" data-min="1">
                                    <option value="new"><strong>Create New Category</strong></option>
                                    @foreach($categories as $cat_data)
                                        <option value="{{ $cat_data->id }}"
                                            {{ in_array($cat_data->id, old('cat_id', $user_categories ?? [])) ? 'selected' : '' }}>
                                            {{ $cat_data->title }}
                                        </option>
                                    @endforeach
                                </select>
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- W9 Section -->
                <div class="form-section">
                    <h3>W9</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_name" class="col-form-label">Name</label>
                                <input id="w9_name" type="text" name="w9_name" placeholder="Name" value="{{ old('w9_name', $user->w9_name) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_business_name" class="col-form-label">Business Name</label>
                                <input id="w9_business_name" type="text" name="w9_business_name" placeholder="Enter Business Name" value="{{ old('w9_business_name', $user->w9_business_name) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_taxClassification" class="col-form-label">Tax Classification</label>
                                <div class="checkbox-group">
                                    <input type="radio" id="individual" {{ old('w9_taxClassification', $user->w9_tax_classification) == 'individual' ? 'checked' : '' }} value="individual" name="w9_taxClassification">
                                    <label for="individual">Individual/sole proprietor</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="radio" id="c-corp" {{ old('w9_taxClassification', $user->w9_tax_classification) == 'c-corp' ? 'checked' : '' }} value="c-corp" name="w9_taxClassification">
                                    <label for="c-corp">C Corporation</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="radio" id="s-corp" {{ old('w9_taxClassification', $user->w9_tax_classification) == 's-corp' ? 'checked' : '' }} value="s-corp" name="w9_taxClassification">
                                    <label for="s-corp">S Corporation</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="radio" id="partnership" {{ old('w9_taxClassification', $user->w9_tax_classification) == 'partnership' ? 'checked' : '' }} value="partnership" name="w9_taxClassification">
                                    <label for="partnership">Partnership</label>
                                </div>
                                <div class="checkbox-group">
                                    <input type="radio" id="trust" {{ old('w9_taxClassification', $user->w9_tax_classification) == 'trust' ? 'checked' : '' }} value="trust" name="w9_taxClassification">
                                    <label for="trust">Trust/estate</label>
                                </div>
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_streetAddress" class="col-form-label">Street Address</label>
                                <input id="w9_streetAddress" type="text" name="w9_streetAddress" placeholder="Enter Street Address" value="{{ old('w9_streetAddress', $user->w9_street_address) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_city" class="col-form-label">City</label>
                                <input id="w9_city" type="text" name="w9_city" placeholder="Enter City" value="{{ old('w9_city', $user->w9_city) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_state" class="col-form-label">State</label>
                                <input id="w9_state" type="text" name="w9_state" placeholder="Enter State" value="{{ old('w9_state', $user->w9_state) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_zipCode" class="col-form-label">ZipCode</label>
                                <input id="w9_zipCode" type="text" name="w9_zipCode" placeholder="Enter ZipCode" value="{{ old('w9_zipCode', $user->w9_zip_code) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="w9_tin" class="col-form-label">TIN</label>
                                <input id="w9_tin" type="text" name="w9_tin" placeholder="Enter TIN" value="{{ old('w9_tin', $user->w9_tin) }}" class="form-control" data-maxlength="255">
                                <span class="text-danger error-message"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Section -->
            <div class="form-section">
                <h3>Status</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="col-form-label">Status<span class="text-danger">*</span></label>
                            <select name="status" class="form-control" data-required="true" data-allowed="active,inactive">
                                <option value="active" {{ old('status', $user->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $user->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group mb-3">
                <button type="submit" class="btn btn-success">Update</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
<style>
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .form-section h3 {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
    .has-error .form-control, .has-error .select2-container--default .select2-selection--multiple {
        border-color: #dc3545;
    }
    .error-message {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    .select2-container {
        width: 100% !important;
    }
    .select2-container--default .select2-selection--multiple {
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        min-height: 38px;
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('backend/js/jquery.min.js') }}"></script>
<script src="{{ asset('backend/js/select2.min.js') }}"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&libraries=places"></script>

<script>
$(document).ready(function() {
    try {
        // Initialize Select2
        $('.select2').each(function() {
            $(this).select2({
                placeholder: $(this).find('option:first').text(),
                allowClear: true,
                width: '100%'
            });
        });

        // Reinitialize Select2 when role changes
        function reinitializeSelect2() {
            $('.select2').each(function() {
                if ($(this).data('select2')) {
                    $(this).select2('destroy');
                }
                $(this).select2({
                    placeholder: $(this).find('option:first').text(),
                    allowClear: true,
                    width: '100%'
                });
            });
        }

        // Same as Billing Address checkbox
        $('#same_as_billing').on('change', function() {
            const shippingFields = ['shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip', 'shipping_long', 'shipping_lat'];
            const billingFields = ['billing_address', 'billing_city', 'billing_state', 'billing_zip', 'billing_long', 'billing_lat'];

            if (this.checked) {
                shippingFields.forEach((field, index) => {
                    $(`input[name="${field}"]`).val($(`input[name="${billingFields[index]}"]`).val());
                });
            } else {
                shippingFields.forEach(field => {
                    $(`input[name="${field}"]`).val('');
                });
            }
        });

        // Toggle user details and salesman based on role
        function toggleUserDetails() {
            const role = $('#role').val();
            const rolesToHideDetails = ['admin', 'office', 'salesman'];
            const userDetails = $('#user_details');
            const salesmanSection = $('.salesman');

            if (rolesToHideDetails.includes(role)) {
                userDetails.hide();
                salesmanSection.hide();
                $('#salesman_id').removeAttr('data-conditional-required');
            } else {
                userDetails.show();
                if (['customer', 'user'].includes(role)) {
                    salesmanSection.show();
                    $('#salesman_id').attr('data-conditional-required', 'role:customer,user');
                } else {
                    salesmanSection.hide();
                    $('#salesman_id').removeAttr('data-conditional-required');
                }
                reinitializeSelect2();
            }
        }

        $('#role').on('change', toggleUserDetails);

        // Initialize visibility based on old input or default
        const oldRole = '{{ old('role', $user->role) }}';
        if (oldRole) {
            $('#role').val(oldRole).trigger('change');
        } else {
            toggleUserDetails();
        }

        // Form validation
        $('#userForm').on('submit', function(e) {
            e.preventDefault();
            let isValid = true;
            let firstInvalidField = null;
            $('.error-message').empty();
            $('.form-group').removeClass('has-error');

            // Validation rules
            const fields = [
                { selector: '[name="first_name"]', rules: { required: true, maxlength: 255 }, label: 'First Name' },
                { selector: '[name="last_name"]', rules: { required: true, maxlength: 255 }, label: 'Last Name' },
                { selector: '[name="email"]', rules: { required: true, email: true, maxlength: 255 }, label: 'Email' },
                { selector: '[name="status"]', rules: { required: true, allowed: ['active', 'inactive'] }, label: 'Status' },
                { selector: '[name="role"]', rules: { required: {{ Auth::user()->role == 'admin' ? 'true' : 'false' }}, allowed: ['admin', 'user', 'customer', 'salesman', 'office', 'picker'] }, label: 'Role' },
                { selector: '[name="salesman_id"]', rules: { conditionalRequired: 'role:customer,user' }, label: 'Salesman' },
                { selector: '[name="company_name"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Company Name' },
                { selector: '[name="billing_address"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Billing Address' },
                { selector: '[name="billing_city"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Billing City' },
                { selector: '[name="billing_state"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Billing State' },
                { selector: '[name="billing_zip"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 10 }, label: 'Billing ZIP Code' },
                { selector: '[name="shipping_address"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Shipping Address' },
                { selector: '[name="shipping_city"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Shipping City' },
                { selector: '[name="shipping_state"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 255 }, label: 'Shipping State' },
                { selector: '[name="shipping_zip"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 10 }, label: 'Shipping ZIP Code' },
                { selector: '[name="contact_phone"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', maxlength: 20 }, label: 'Contact Phone Number' },
                { selector: '[name="price_level_lists[]"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', min: 1 }, label: 'Price Level Lists' },
                { selector: '[name="cat_id[]"]', rules: { conditionalRequired: 'role:not:admin,office,salesman', min: 1 }, label: 'Category' },
                { selector: '[name="account_phone"]', rules: { maxlength: 20 }, label: 'Account Payable Phone Number' },
                { selector: '[name="w9_name"]', rules: { maxlength: 255 }, label: 'W9 Name' },
                { selector: '[name="w9_business_name"]', rules: { maxlength: 255 }, label: 'W9 Business Name' },
                { selector: '[name="w9_taxClassification"]', rules: { maxlength: 255 }, label: 'W9 Tax Classification' },
                { selector: '[name="w9_streetAddress"]', rules: { maxlength: 255 }, label: 'W9 Street Address' },
                { selector: '[name="w9_city"]', rules: { maxlength: 255 }, label: 'W9 City' },
                { selector: '[name="w9_state"]', rules: { maxlength: 255 }, label: 'W9 State' },
                { selector: '[name="w9_zipCode"]', rules: { maxlength: 255 }, label: 'W9 ZipCode' },
                { selector: '[name="w9_tin"]', rules: { maxlength: 255 }, label: 'W9 TIN' },
            ];

            // Helper functions
            const isEmailValid = (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
            const role = $('#role').val();
            const minimalValidationRoles = ['admin', 'salesman', 'office'];

            // Validate fields
            fields.forEach(field => {
                try {
                    const $input = $(field.selector);
                    const isRadio = $input.is(':radio');
                    const value = isRadio ? $(`${field.selector}:checked`).val() : $input.val();
                    const rules = field.rules;
                    let error = '';

                    // Skip validation for optional fields when role is admin, salesman, or office
                    if (minimalValidationRoles.includes(role) && !['first_name', 'last_name', 'email', 'status', 'role'].includes(field.selector.replace(/\[.*\]/, ''))) {
                        return;
                    }

                    // Required validation
                    if (rules.required && (!value || value === '' || (Array.isArray(value) && value.length === 0))) {
                        error = `${field.label} is required.`;
                    }

                    // Conditional required
                    if (rules.conditionalRequired && !error) {
                        const [key, condition] = rules.conditionalRequired.split(':');
                        const values = condition.split(',');
                        const isNotCondition = condition.startsWith('not');
                        const conditionMet = isNotCondition ? !values.includes(role) : values.includes(role);
                        if (key === 'role' && conditionMet) {
                            if (!value || value === '' || (Array.isArray(value) && value.length === 0)) {
                                error = `${field.label} is required.`;
                            }
                        }
                    }

                    // Email validation
                    if (rules.email && value && !isEmailValid(value)) {
                        error = 'Please enter a valid email.';
                    }

                    // Max length validation
                    if (rules.maxlength && value && value.length > rules.maxlength) {
                        error = `${field.label} must not exceed ${rules.maxlength} characters.`;
                    }

                    // Allowed values validation
                    if (rules.allowed && value && !rules.allowed.includes(value)) {
                        error = `Invalid ${field.label}. Allowed: ${rules.allowed}.`;
                    }

                    // Min array validation
                    if (rules.min && Array.isArray(value) && value.length < rules.min) {
                        error = `Please select at least ${rules.min} ${field.label} option${rules.min > 1 ? 's' : ''}.`;
                    }

                    if (error) {
                        isValid = false;
                        $input.closest('.form-group').addClass('has-error');
                        $input.siblings('.error-message').text(error);
                        if (!firstInvalidField) {
                            firstInvalidField = $input;
                        }
                    }
                } catch (err) {
                    console.error(`Error validating ${field.label}:`, err);
                }
            });

            // Proceed with form submission or scrolling
            if (isValid) {
                $('#userForm')[0].submit();
            } else if (firstInvalidField) {
                $('html, body').animate({
                    scrollTop: firstInvalidField.offset().top - 100
                }, 500);
            }
        });

        // Google Maps Autocomplete
        function initAutocomplete() {
            try {
                const billingInput = document.getElementById('billing_address');
                const shippingInput = document.getElementById('shipping_address');
                if (!billingInput || !shippingInput) {
                    console.error('Autocomplete inputs not found');
                    return;
                }

                const autocompleteBilling = new google.maps.places.Autocomplete(billingInput);
                const autocompleteShipping = new google.maps.places.Autocomplete(shippingInput);

                autocompleteBilling.addListener('place_changed', function() {
                    const place = autocompleteBilling.getPlace();
                    if (!place.geometry) {
                        console.warn('No geometry for billing address');
                        return;
                    }
                    document.getElementById('billing_lat').value = place.geometry.location.lat();
                    document.getElementById('billing_long').value = place.geometry.location.lng();
                    place.address_components.forEach(component => {
                        const types = component.types;
                        if (types.includes('locality')) {
                            document.getElementById('billing_city').value = component.long_name;
                        }
                        if (types.includes('administrative_area_level_1')) {
                            document.getElementById('billing_state').value = component.long_name;
                        }
                        if (types.includes('postal_code')) {
                            document.getElementById('billing_zip').value = component.long_name;
                        }
                    });
                });

                autocompleteShipping.addListener('place_changed', function() {
                    const place = autocompleteShipping.getPlace();
                    if (!place.geometry) {
                        console.warn('No geometry for shipping address');
                        return;
                    }
                    document.getElementById('shipping_lat').value = place.geometry.location.lat();
                    document.getElementById('shipping_long').value = place.geometry.location.lng();
                    place.address_components.forEach(component => {
                        const types = component.types;
                        if (types.includes('locality')) {
                            document.getElementById('shipping_city').value = component.long_name;
                        }
                        if (types.includes('administrative_area_level_1')) {
                            document.getElementById('shipping_state').value = component.long_name;
                        }
                        if (types.includes('postal_code')) {
                            document.getElementById('shipping_zip').value = component.long_name;
                        }
                    });
                });
            } catch (err) {
                console.error('Error initializing Google Maps Autocomplete:', err);
            }
        }

        initAutocomplete();
    } catch (err) {
        console.error('Initialization error:', err);
    }

    // Handle Create New Category
    $('#cat_id').on('change', function() {
        const selectedCats = $(this).val() || [];
        if(selectedCats.includes('new')){
            // Remove 'new' from selection
            const filteredCats = selectedCats.filter(cat => cat !== 'new');
            $(this).val(filteredCats).trigger('change');

            // Show create category modal or redirect
            if(confirm('Do you want to create a new category? You will be redirected to the category creation page.')){
                window.open('{{ route("category.create") }}', '_blank');
            }
        }
    });

    // Handle Create New Salesman
    $('#salesman_id').on('change', function() {
        const selectedSalesman = $(this).val();
        if(selectedSalesman === 'new'){
            $(this).val('').trigger('change');

            // Show create salesman modal or redirect
            if(confirm('Do you want to create a new salesman? You will be redirected to the user creation page.')){
                window.open('{{ route("users.create") }}?role=salesman', '_blank');
            }
        }
    });
});
</script>
@endpush
