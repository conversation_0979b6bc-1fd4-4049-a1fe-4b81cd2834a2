@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Edit User</h5>
    <div class="card-body">
        <form method="post" action="{{ route('salesman.users.update', $user->id) }}">
            @csrf
            @method('PATCH')

            <!-- Personal Information Section -->
            <div class="form-section">
                <h3>Personal Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="first_name" class="col-form-label">First Name<span class="text-danger">*</span></label>
                            <input id="first_name" type="text" name="first_name" placeholder="Enter first name" value="{{ old('first_name', $user->first_name) }}" class="form-control">
                            @error('first_name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="last_name" class="col-form-label">Last Name<span class="text-danger">*</span></label>
                            <input id="last_name" type="text" name="last_name" placeholder="Enter last name" value="{{ old('last_name', $user->last_name) }}" class="form-control">
                            @error('last_name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email" class="col-form-label">Email<span class="text-danger">*</span></label>
                            <input id="email" type="email" name="email" placeholder="Enter email" value="{{ old('email', $user->email) }}" class="form-control" disabled>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Company Information Section -->
            <div class="form-section">
                <h3>Company Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="company_name" class="col-form-label">Company Name</label>
                            <input id="company_name" type="text" name="company_name" placeholder="Enter company name" value="{{ old('company_name', $user->company_name) }}" class="form-control">
                            @error('company_name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Billing Address Section -->
            <div class="form-section">
                <h3>Billing Address</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="billing_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                            <input id="billing_address" type="text" name="billing_address" placeholder="Enter billing address" value="{{ old('billing_address', $user->billing_address) }}" class="form-control">
                            <input type="hidden" id="billing_lat" name="billing_lat" value="{{old('billing_lat',$user->billing_lat)}}">
                            <input type="hidden" id="billing_long" name="billing_long" value="{{old('billing_long',$user->billing_long)}}">
                            @error('billing_address')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="billing_city" class="col-form-label">City<span class="text-danger">*</span></label>
                            <input id="billing_city" type="text" name="billing_city" placeholder="Enter city" value="{{ old('billing_city', $user->billing_city) }}" class="form-control">
                            @error('billing_city')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="billing_state" class="col-form-label">State<span class="text-danger">*</span></label>
                            <input id="billing_state" type="text" name="billing_state" placeholder="Enter state" value="{{ old('billing_state', $user->billing_state) }}" class="form-control">
                            @error('billing_state')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="billing_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                            <input id="billing_zip" type="text" name="billing_zip" placeholder="Enter ZIP code" value="{{ old('billing_zip', $user->billing_zip) }}" class="form-control">
                            @error('billing_zip')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shipping Address Section -->
            <div class="form-section">
                <h3>Shipping Address</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="shipping_address" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                            <input id="shipping_address" type="text" name="shipping_address" placeholder="Enter shipping address" value="{{ old('shipping_address', $user->shipping_address) }}" class="form-control">
                            <input type="hidden" id="shipping_lat" name="shipping_lat" value="{{old('shipping_lat',$user->shipping_lat)}}">
                            <input type="hidden" id="shipping_long" name="shipping_long" value="{{old('shipping_long',$user->shipping_long)}}">
                            @error('shipping_address')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="shipping_city" class="col-form-label">City<span class="text-danger">*</span></label>
                            <input id="shipping_city" type="text" name="shipping_city" placeholder="Enter city" value="{{ old('shipping_city', $user->shipping_city) }}" class="form-control">
                            @error('shipping_city')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="shipping_state" class="col-form-label">State<span class="text-danger">*</span></label>
                            <input id="shipping_state" type="text" name="shipping_state" placeholder="Enter state" value="{{ old('shipping_state', $user->shipping_state) }}" class="form-control">
                            @error('shipping_state')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="shipping_zip" class="col-form-label">ZIP Code<span class="text-danger">*</span></label>
                            <input id="shipping_zip" type="text" name="shipping_zip" placeholder="Enter ZIP code" value="{{ old('shipping_zip', $user->shipping_zip) }}" class="form-control">
                            @error('shipping_zip')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="form-section">
                <h3>Contact Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="contact_phone" class="col-form-label">Contact Phone Number<span class="text-danger">*</span></label>
                            <input id="contact_phone" type="text" name="contact_phone" placeholder="Enter contact phone number" value="{{ old('contact_phone', $user->contact_phone) }}" class="form-control">
                            @error('contact_phone')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="account_phone" class="col-form-label">Account Payable Phone Number</label>
                            <input id="account_phone" type="text" name="account_phone" placeholder="Enter account payable phone number" value="{{ old('account_phone', $user->account_phone) }}" class="form-control">
                            @error('account_phone')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>Connections</h3>
                <div class="row">
                    <!-- Price Level Lists -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="price_level_lists">Price Level Lists</label>
                            <select name="price_level_lists[]" id="price_level_lists" class="form-control select2" multiple required>
                                @foreach($price_level_lists as $price_level_list)
                                    <option value="{{ $price_level_list->id }}"
                                        {{ in_array($price_level_list->id, old('price_level_lists', $user_price_level_lists ?? [])) ? 'selected' : '' }}>
                                        {{ $price_level_list->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('price_level_lists')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>

                    <!-- Categories -->
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cat_id">Category <span class="text-danger">*</span></label>
                            <select name="cat_id[]" id="cat_id" class="form-control select2" multiple required>
                                @foreach($categories as $cat_data)
                                    <option value="{{ $cat_data->id }}"
                                        {{ in_array($cat_data->id, old('cat_id', $user_categories ?? [])) ? 'selected' : '' }}>
                                        {{ $cat_data->title }}
                                    </option>
                                @endforeach
                            </select>
                            @error('cat_id')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-section">
                <h3>W9</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_name" class="col-form-label">Name<span class="text-danger">*</span></label>
                            <input id="w9_name" type="text" name="w9_name" placeholder="Name" value="{{ old('w9_name',$user->w9_name) }}" class="form-control">
                            @error('w9_name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_business_name" class="col-form-label">City<span class="text-danger">*</span></label>
                            <input id="w9_business_name" type="text" name="w9_business_name" placeholder="Enter Business Name" value="{{ old('w9_business_name',$user->w9_business_name) }}" class="form-control">
                            @error('w9_business_name')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="taxClassification" class="col-form-label">Tax Classification<span class="text-danger">*</span></label>
                            <div class="checkbox-group">
                                <input type="radio" id="individual" {{old('w9_taxClassification',$user->w9_tax_classification) == 'individual' ? 'checked' : ''}} value="individual" name="w9_taxClassification">
                                <label for="individual">Individual/sole proprietor</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="radio" id="c-corp" {{old('w9_taxClassification',$user->w9_tax_classification) == 'c-corp' ? 'checked' : ''}} value="c-corp" name="w9_taxClassification">
                                <label for="c-corp">C Corporation</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="radio" id="s-corp" {{old('w9_taxClassification',$user->w9_tax_classification) == 's-corp' ? 'checked' : ''}} value="s-corp" name="w9_taxClassification">
                                <label for="s-corp">S Corporation</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="radio" id="partnership" {{old('w9_taxClassification',$user->w9_tax_classification) == 'partnership' ? 'checked' : ''}} value="partnership" name="w9_taxClassification">
                                <label for="partnership">Partnership</label>
                            </div>
                            <div class="checkbox-group">
                                <input type="radio" id="trust" {{old('w9_taxClassification',$user->w9_tax_classification) == 'trust' ? 'checked' : ''}} value="trust" name="w9_taxClassification">
                                <label for="trust">Trust/estate</label>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_streetAddress" class="col-form-label">Street Address<span class="text-danger">*</span></label>
                            <input id="w9_streetAddress" type="text" name="w9_streetAddress" placeholder="Enter Street Address" value="{{ old('w9_streetAddress',$user->w9_street_address) }}" class="form-control">
                            @error('w9_streetAddress')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_city" class="col-form-label">City<span class="text-danger">*</span></label>
                            <input id="w9_city" type="text" name="w9_city" placeholder="Enter City" value="{{ old('w9_city',$user->w9_city) }}" class="form-control">
                            @error('w9_city')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_state" class="col-form-label">State<span class="text-danger">*</span></label>
                            <input id="w9_state" type="text" name="w9_state" placeholder="Enter State" value="{{ old('w9_state',$user->w9_state) }}" class="form-control">
                            @error('w9_state')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_zipCode" class="col-form-label">ZipCode<span class="text-danger">*</span></label>
                            <input id="w9_zipCode" type="text" name="w9_zipCode" placeholder="Enter ZipCode" value="{{ old('w9_zipCode',$user->w9_zip_code) }}" class="form-control">
                            @error('w9_zipCode')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="w9_tin" class="col-form-label">TIN<span class="text-danger">*</span></label>
                            <input id="w9_tin" type="text" name="w9_tin" placeholder="Enter TIN" value="{{ old('w9_tin',$user->w9_tin) }}" class="form-control">
                            @error('w9_tin')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Section -->
            <div class="form-section">
                <h3>Status</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="col-form-label">Status<span class="text-danger">*</span></label>
                            <select name="status" class="form-control">
                                <option value="active" {{ old('status', $user->status) === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ old('status', $user->status) === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                            @error('status')
                                <span class="text-danger">{{ $message }}</span>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group mb-3">
                <button type="submit" class="btn btn-success">Update</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('scripts')
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCdSGqiP_9s6qf1RIF70fG6BmFsjzR9QxU&libraries=places"></script>

<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{ asset('backend/js/select2.min.js') }}"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();

        $('form').on('submit', function(e) {
            let isValid = true;

            $('.select2').each(function() {
                if ($(this).prop('required') && $(this).val() === null) {
                    isValid = false;
                    $(this).closest('.form-group').addClass('has-error');
                    $(this).next('.select2-container').after('<span class="text-danger">This field is required.</span>');
                } else {
                    $(this).closest('.form-group').removeClass('has-error');
                    $(this).next('.select2-container').next('.text-danger').remove();
                }
            });

            if (!isValid) {
                e.preventDefault();
            }
        });

        $('#role').on('change', function() {
            if(this.value == 'customer' || this.value == 'user' ){
            $('.salesman').show();
            $('#salesman_id').prop('required', true);
        } else {
            $('.salesman').hide();
            $('#salesman_id').prop('required', false);
        }
        });

        if('{{old('role',$user->role)}}' == 'customer' || '{{old('role',$user->role)}}' == 'user' ){
            $('.salesman').show();
            $('#salesman_id').prop('required', true);
        } else {
            $('.salesman').hide();
            $('#salesman_id').prop('required', false);
        }
    })
    function initAutocomplete() {
        var input = document.getElementById('billing_address');
        var autocomplete = new google.maps.places.Autocomplete(input);

        var input1 = document.getElementById('shipping_address');
        var autocomplete1 = new google.maps.places.Autocomplete(input1);

        autocomplete.addListener('place_changed', function () {
            var place = autocomplete.getPlace();
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();
            document.getElementById("billing_lat").value = lat;
            document.getElementById("billing_long").value = lng;
            place.address_components.forEach(component => {
                var types = component.types;
                if (types.includes("locality")) {
                    document.getElementById('billing_city').value = component.long_name;
                }
                if (types.includes("administrative_area_level_1")) {
                    document.getElementById('billing_state').value = component.long_name;
                }
                if (types.includes("postal_code")) {
                    document.getElementById('billing_zip').value = component.long_name;
                }
            });
        });

        autocomplete1.addListener('place_changed', function () {
            var place = autocomplete1.getPlace();
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();
            document.getElementById("shipping_lat").value = lat;
            document.getElementById("shipping_long").value = lng;
            place.address_components.forEach(component => {
                var types = component.types;
                if (types.includes("locality")) {
                    document.getElementById('shipping_city').value = component.long_name;
                }
                if (types.includes("administrative_area_level_1")) {
                    document.getElementById('shipping_state').value = component.long_name;
                }
                if (types.includes("postal_code")) {
                    document.getElementById('shipping_zip').value = component.long_name;
                }
            });
        });
    }

    document.addEventListener("DOMContentLoaded", function () {
        initAutocomplete();
    });
</script>
@endpush

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
<style>
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .form-section h3 {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
</style>
@endpush
