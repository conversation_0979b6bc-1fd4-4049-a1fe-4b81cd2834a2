<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('w9_name')->nullable()->default(null);
            $table->string('w9_business_name')->nullable()->default(null);
            $table->string('w9_tax_classification')->nullable()->default(null);
            $table->string('w9_street_address')->nullable()->default(null);
            $table->string('w9_city')->nullable()->default(null);
            $table->string('w9_state')->nullable()->default(null);
            $table->string('w9_zip_code')->nullable()->default(null);
            $table->string('w9_tin')->nullable()->default(null);


        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('w9_name');
            $table->dropColumn('w9_business_name');
            $table->dropColumn('w9_tax_classification');
            $table->dropColumn('w9_street_address');
            $table->dropColumn('w9_city');
            $table->dropColumn('w9_state');
            $table->dropColumn('w9_zip_code');
            $table->dropColumn('w9_tin');
        });
    }
};
