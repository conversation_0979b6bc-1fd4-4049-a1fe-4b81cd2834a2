<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderItem extends Model
{
    use HasFactory;

    protected $appends = ['item_image'];

    protected $fillable = [
        'order_id', 'product_id','product_name', 'price', 'quantity', 'total', 'options', 'is_returned', 'color'
    ];

    protected $casts = [
        'options' => 'array',
        'is_returned' => 'boolean'
    ];

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function color_name()
    {
        return $this->belongsTo(Color::class, 'color');
    }

    public function getItemImageAttribute()
    {
        return ItemColor::where('color_id', $this->color)->where('product_id', $this->product_id)->first()->photo ?? null;
    }

    public function return_requests()
    {
        return $this->hasMany(ReturnRequest::class);
    }

    public function returns()
    {
        return $this->hasMany(ReturnRequest::class)->where('status', 'approved');
    }

    public function getReturnStatusAttribute()
    {
        return $this->returns->count() > 0 ? 'returned' : 'none';
    }
}
