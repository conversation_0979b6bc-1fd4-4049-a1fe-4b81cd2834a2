<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\{Order,User};

class SalesmanController extends Controller
{
    public function index()
    {
        $orders = Order::where('salesman_id',Auth()->user()->id)->count();
        $draft_orders = Order::where('salesman_id',Auth()->user()->id)->where('safe_for_future',1)->count();
        $latest_orders = Order::where('salesman_id',Auth()->user()->id)->latest()->limit(5)->get();
        $customers = User::whereIn('role', ['user','customer'])->where('salesman_id',Auth()->user()->id)->count();
        return view('salesman.dashboard',compact('orders','latest_orders','customers','draft_orders'));
    }
}
