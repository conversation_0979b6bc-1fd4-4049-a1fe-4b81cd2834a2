<?php

namespace App\Listeners;

use App\Events\VisitReminder;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendVisitReminderEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\VisitReminder  $event
     * @return void
     */
    public function handle(VisitReminder $event)
    {
        $visit = $event->visit;

        if (!$visit->customer || !$visit->customer->email) {
            return;
        }

        $data = [
            'visit' => $visit,
        ];

        Mail::to($visit->customer->email)->queue(new MailQueue(
            'backend.emails.visit_reminder',
            $data,
            'Visit Reminder - Lamart Manufacturing'
        ));
    }
}
