<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\User;
use App\Models\Product;
use App\Events\OrderCreated;
use App\Events\OrderStatusChanged;
use App\Events\OrderAssignedToSalesman;
use App\Events\UserAssignedToSalesman;
use App\Events\ReturnRequestCreated;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class TestEmailSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'email:test-system 
                            {--create-test-data : Create test users and orders}
                            {--test-events : Test all email events}
                            {--test-order-flow : Test complete order flow}
                            {--email= : Email address to send test emails to}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete email system with real data';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🧪 Email System Testing Tool');
        $this->info('================================');

        if ($this->option('create-test-data')) {
            $this->createTestData();
        }

        if ($this->option('test-events')) {
            $this->testAllEvents();
        }

        if ($this->option('test-order-flow')) {
            $this->testCompleteOrderFlow();
        }

        if (!$this->option('create-test-data') && !$this->option('test-events') && !$this->option('test-order-flow')) {
            $this->showMenu();
        }

        return 0;
    }

    /**
     * Show interactive menu
     */
    private function showMenu()
    {
        $choice = $this->choice('What would you like to test?', [
            'Create test data',
            'Test all email events',
            'Test complete order flow',
            'Check queue status',
            'View email templates',
            'Exit'
        ]);

        switch ($choice) {
            case 'Create test data':
                $this->createTestData();
                break;
            case 'Test all email events':
                $this->testAllEvents();
                break;
            case 'Test complete order flow':
                $this->testCompleteOrderFlow();
                break;
            case 'Check queue status':
                $this->checkQueueStatus();
                break;
            case 'View email templates':
                $this->showEmailTemplates();
                break;
            case 'Exit':
                $this->info('Goodbye!');
                return;
        }

        // Show menu again
        $this->showMenu();
    }

    /**
     * Create test data
     */
    private function createTestData()
    {
        $this->info('📝 Creating test data...');

        try {
            DB::beginTransaction();

            // Create test customer
            $customer = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'first_name' => 'Test',
                    'last_name' => 'Customer',
                    'role' => 'user',
                    'status' => 'active',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now()
                ]
            );

            // Create test salesman
            $salesman = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'first_name' => 'Test',
                    'last_name' => 'Salesman',
                    'role' => 'salesman',
                    'status' => 'active',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now()
                ]
            );

            // Create test admin
            $admin = User::firstOrCreate(
                ['email' => '<EMAIL>'],
                [
                    'first_name' => 'Test',
                    'last_name' => 'Admin',
                    'role' => 'admin',
                    'status' => 'active',
                    'password' => bcrypt('password'),
                    'email_verified_at' => now()
                ]
            );

            // Create test order
            $order = Order::create([
                'order_number' => 'TEST-' . strtoupper(Str::random(8)),
                'user_id' => $customer->id,
                'salesman_id' => $salesman->id,
                'first_name' => $customer->first_name,
                'last_name' => $customer->last_name,
                'email' => $customer->email,
                'phone' => '+****************',
                'address1' => '123 Test Street',
                'address2' => 'Apt 1',
                'post_code' => '12345',
                'country' => 'United States',
                'sub_total' => 100.00,
                'delivery_charge' => 10.00,
                'total_amount' => 110.00,
                'quantity' => 2,
                'status' => 'new',
                'payment_status' => 'pending'
            ]);

            DB::commit();

            $this->info('✅ Test data created successfully!');
            $this->table(['Type', 'Email', 'ID'], [
                ['Customer', $customer->email, $customer->id],
                ['Salesman', $salesman->email, $salesman->id],
                ['Admin', $admin->email, $admin->id],
                ['Order', $order->order_number, $order->id]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            $this->error('❌ Failed to create test data: ' . $e->getMessage());
        }
    }

    /**
     * Test all email events
     */
    private function testAllEvents()
    {
        $this->info('🎯 Testing all email events...');

        $order = Order::latest()->first();
        if (!$order) {
            $this->error('❌ No orders found. Run with --create-test-data first.');
            return;
        }

        $customer = $order->customer ?? User::where('role', 'user')->first();
        $salesman = User::where('role', 'salesman')->first();

        if (!$customer || !$salesman) {
            $this->error('❌ Missing test users. Run with --create-test-data first.');
            return;
        }

        $this->info('Testing OrderCreated event...');
        event(new OrderCreated($order));
        $this->info('✅ OrderCreated event dispatched');

        $this->info('Testing OrderStatusChanged event...');
        event(new OrderStatusChanged($order, 'new', 'process'));
        $this->info('✅ OrderStatusChanged event dispatched');

        $this->info('Testing OrderAssignedToSalesman event...');
        event(new OrderAssignedToSalesman($order, $salesman));
        $this->info('✅ OrderAssignedToSalesman event dispatched');

        $this->info('Testing UserAssignedToSalesman event...');
        event(new UserAssignedToSalesman($customer, $salesman));
        $this->info('✅ UserAssignedToSalesman event dispatched');

        $this->info('Testing ReturnRequestCreated event...');
        $returns = [['product_id' => 1, 'quantity' => 1, 'reason' => 'Defective']];
        event(new ReturnRequestCreated($order, $returns, 'Test return request'));
        $this->info('✅ ReturnRequestCreated event dispatched');

        $this->info('🎉 All events tested successfully!');
        $this->warn('📧 Check your queue and email logs to verify emails were sent.');
    }

    /**
     * Test complete order flow
     */
    private function testCompleteOrderFlow()
    {
        $this->info('🔄 Testing complete order flow...');

        $order = Order::latest()->first();
        if (!$order) {
            $this->error('❌ No orders found. Run with --create-test-data first.');
            return;
        }

        $statuses = ['new', 'process', 'delivered'];
        $currentStatus = $order->status;

        foreach ($statuses as $status) {
            if ($status !== $currentStatus) {
                $this->info("Changing order status from {$currentStatus} to {$status}...");
                
                $oldStatus = $order->status;
                $order->status = $status;
                $order->save();
                
                event(new OrderStatusChanged($order, $oldStatus, $status));
                $this->info("✅ Status changed to {$status}");
                
                $currentStatus = $status;
                sleep(1); // Small delay between status changes
            }
        }

        $this->info('🎉 Complete order flow tested successfully!');
    }

    /**
     * Check queue status
     */
    private function checkQueueStatus()
    {
        $this->info('📊 Queue Status:');

        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();

        $this->table(['Metric', 'Count'], [
            ['Pending Jobs', $pendingJobs],
            ['Failed Jobs', $failedJobs]
        ]);

        if ($pendingJobs > 0) {
            $this->info('💡 Process queue with: php artisan queue:work');
        }

        if ($failedJobs > 0) {
            $this->warn("⚠️  You have {$failedJobs} failed jobs. Check the queue monitor.");
        }
    }

    /**
     * Show available email templates
     */
    private function showEmailTemplates()
    {
        $this->info('📧 Available Email Templates:');

        $templates = [
            'Order Emails' => [
                'backend.emails.order_details_customer',
                'backend.emails.order_assigned_salesman',
                'backend.emails.salesman_assigned_customer',
                'backend.emails.order_status_changed_customer',
                'backend.emails.order_picker_notification'
            ],
            'User Management' => [
                'emails.registered',
                'emails.created',
                'emails.user_status',
                'backend.emails.user_assigned_salesman_customer'
            ],
            'Other' => [
                'backend.emails.salesman-visit-reminder',
                'backend.emails.salesman-return-request',
                'backend.emails.promotional_email'
            ]
        ];

        foreach ($templates as $category => $templateList) {
            $this->info("\n{$category}:");
            foreach ($templateList as $template) {
                $this->line("  • {$template}");
            }
        }

        $this->info("\n💡 Preview templates at: /email-testing");
        $this->info("💡 Monitor queues at: /queue-monitor");
    }
}
