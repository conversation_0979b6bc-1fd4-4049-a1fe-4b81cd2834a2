/* =====================================
LAMART BACKEND THEME
Admin & Salesman Dashboard Theme
Primary Color: #4D734E (Forest Green)
Secondary Color: #5A5863 (Charcoal Grey)
Accent Color: #000000 (Pure Black)
========================================*/

/* CSS Variables for Backend Theme */
:root {
    --lamart-primary: #4d734e;
    --lamart-primary-dark: #3d5a3e;
    --lamart-primary-light: #6b9a6d;
    --lamart-secondary: #5a5863;
    --lamart-secondary-light: #8a8a95;
    --lamart-secondary-dark: #4a4a52;
    --lamart-accent: #000000;
    --lamart-light: #f8f9fa;
    --lamart-white: #ffffff;
    --lamart-success: #4d734e;
    --lamart-warning: #ffc107;
    --lamart-danger: #dc3545;
    --lamart-info: #17a2b8;
    --lamart-border: #e9ecef;
    --lamart-text: #212529;
    --lamart-text-muted: #5a5863;
    --lamart-shadow: 0 0.15rem 1.75rem 0 rgba(77, 115, 78, 0.15);
    --lamart-shadow-lg: 0 1rem 3rem rgba(77, 115, 78, 0.175);
    --lamart-gradient: linear-gradient(135deg, #4d734e 0%, #3d5a3e 100%);
    --lamart-gradient-secondary: linear-gradient(
        135deg,
        #5a5863 0%,
        #4a4a52 100%
    );
}

/* =====================================
SIDEBAR STYLING
========================================*/

/* Main Sidebar */
.bg-gradient-primary {
    background: var(--lamart-gradient) !important;
}

.sidebar-dark .sidebar-brand {
    color: var(--lamart-white) !important;
}

.sidebar-dark .sidebar-brand-icon {
    color: var(--lamart-white) !important;
}

.sidebar-dark .nav-item .nav-link {
    color: rgba(255, 255, 255, 0.8) !important;
    border-radius: 0.5rem;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
}

.sidebar-dark .nav-item .nav-link:hover,
.sidebar-dark .nav-item .nav-link:focus {
    color: var(--lamart-white) !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(5px);
}

.sidebar-dark .nav-item .nav-link.active {
    color: var(--lamart-white) !important;
    background-color: rgba(255, 255, 255, 0.2) !important;
    font-weight: 600;
}

.sidebar-dark .nav-item .nav-link[data-toggle="collapse"]::after {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Sidebar Divider */
.sidebar-divider {
    border-color: rgba(255, 255, 255, 0.2) !important;
}

/* =====================================
TOPBAR STYLING
========================================*/

.topbar {
    background: var(--lamart-white) !important;
    border-bottom: 1px solid var(--lamart-border);
    box-shadow: var(--lamart-shadow);
}

.topbar .navbar-search .form-control {
    border: 2px solid var(--lamart-border);
    border-radius: 2rem;
    background-color: var(--lamart-light);
    transition: all 0.3s ease;
}

.topbar .navbar-search .form-control:focus {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.topbar .navbar-search .btn {
    background: var(--lamart-primary);
    border-color: var(--lamart-primary);
    border-radius: 2rem;
}

.topbar .navbar-search .btn:hover {
    background: var(--lamart-primary-dark);
    border-color: var(--lamart-primary-dark);
}

/* User Info Dropdown */
.topbar .dropdown-toggle::after {
    color: var(--lamart-text-muted);
}

.topbar .dropdown-menu {
    border: none;
    box-shadow: var(--lamart-shadow-lg);
    border-radius: 0.75rem;
}

.topbar .dropdown-item {
    color: var(--lamart-text);
    transition: all 0.3s ease;
}

.topbar .dropdown-item:hover {
    background-color: var(--lamart-light);
    color: var(--lamart-primary);
}

/* =====================================
MAIN CONTENT STYLING
========================================*/

#content {
    background-color: var(--lamart-white) !important;
}

/* Page Heading */
.d-sm-flex.align-items-center.justify-content-between.mb-4 h1 {
    color: var(--lamart-text);
    font-weight: 700;
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: var(--lamart-primary);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--lamart-text-muted);
}

/* =====================================
CARDS STYLING
========================================*/

.card {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--lamart-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--lamart-shadow-lg);
}

.card-header {
    background: var(--lamart-gradient);
    color: var(--lamart-white);
    border-radius: 1rem 1rem 0 0 !important;
    border-bottom: none;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Dashboard Cards */
.card.border-left-primary {
    border-left: 0.25rem solid var(--lamart-primary) !important;
}

.card.border-left-success {
    border-left: 0.25rem solid var(--lamart-success) !important;
}

.card.border-left-info {
    border-left: 0.25rem solid var(--lamart-info) !important;
}

.card.border-left-warning {
    border-left: 0.25rem solid var(--lamart-warning) !important;
}

/* Card Text Colors */
.text-primary {
    color: var(--lamart-primary) !important;
}

.text-success {
    color: var(--lamart-success) !important;
}

.text-info {
    color: var(--lamart-info) !important;
}

.text-warning {
    color: var(--lamart-warning) !important;
}

/* =====================================
BUTTONS STYLING
========================================*/

.btn-primary {
    background: var(--lamart-gradient);
    border-color: var(--lamart-primary);
    color: var(--lamart-white);
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover,
.btn-primary:focus {
    background: var(--lamart-primary-dark);
    border-color: var(--lamart-primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 0.5rem 1rem rgba(77, 115, 78, 0.25);
}

.btn-success {
    background: var(--lamart-success);
    border-color: var(--lamart-success);
    border-radius: 0.5rem;
}

.btn-success:hover {
    background: var(--lamart-primary-dark);
    border-color: var(--lamart-primary-dark);
}

.btn-outline-primary {
    color: var(--lamart-primary);
    border-color: var(--lamart-primary);
    border-radius: 0.5rem;
}

.btn-outline-primary:hover {
    background: var(--lamart-primary);
    border-color: var(--lamart-primary);
}

.btn-secondary {
    background: var(--lamart-secondary);
    border-color: var(--lamart-secondary);
    border-radius: 0.5rem;
}

.btn-secondary:hover {
    background: var(--lamart-secondary-dark);
    border-color: var(--lamart-secondary-dark);
}

/* Small Buttons */
.btn-sm {
    border-radius: 0.375rem;
    font-weight: 500;
}

/* =====================================
FORMS STYLING
========================================*/

.form-control {
    border: 2px solid var(--lamart-border);
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.form-label {
    color: var(--lamart-text);
    font-weight: 600;
}

.form-select {
    border: 2px solid var(--lamart-border);
    border-radius: 0.5rem;
}

.form-select:focus {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

/* =====================================
TABLES STYLING
========================================*/

.table {
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: var(--lamart-shadow);
}

.table thead th {
    background: var(--lamart-gradient);
    color: var(--lamart-white);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(77, 115, 78, 0.05);
    transform: scale(1.01);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(77, 115, 78, 0.02);
}

/* DataTables Styling */
.dataTables_wrapper .dataTables_length select,
.dataTables_wrapper .dataTables_filter input {
    border: 2px solid var(--lamart-border);
    border-radius: 0.5rem;
}

.dataTables_wrapper .dataTables_filter input:focus {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--lamart-gradient) !important;
    border-color: var(--lamart-primary) !important;
    color: var(--lamart-white) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--lamart-light) !important;
    border-color: var(--lamart-primary) !important;
    color: var(--lamart-primary) !important;
}

/* =====================================
ALERTS & NOTIFICATIONS
========================================*/

.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid;
    box-shadow: var(--lamart-shadow);
}

.alert-success {
    background-color: rgba(77, 115, 78, 0.1);
    border-left-color: var(--lamart-success);
    color: var(--lamart-success);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left-color: var(--lamart-danger);
    color: var(--lamart-danger);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: var(--lamart-warning);
    color: var(--lamart-warning);
}

.alert-info {
    background-color: rgba(77, 115, 78, 0.1);
    border-left-color: var(--lamart-primary);
    color: var(--lamart-primary);
}

/* Notification Badges */
.badge-danger {
    background-color: var(--lamart-primary) !important;
}

.badge-success {
    background-color: var(--lamart-success) !important;
}

.badge-warning {
    background-color: var(--lamart-warning) !important;
}

.badge-info {
    background-color: var(--lamart-info) !important;
}

/* =====================================
PROGRESS BARS
========================================*/

.progress {
    border-radius: 0.5rem;
    background-color: var(--lamart-light);
}

.progress-bar {
    background: var(--lamart-gradient);
    border-radius: 0.5rem;
}

.progress-bar-success {
    background: var(--lamart-success);
}

.progress-bar-info {
    background: var(--lamart-info);
}

.progress-bar-warning {
    background: var(--lamart-warning);
}

.progress-bar-danger {
    background: var(--lamart-danger);
}

/* =====================================
MODALS
========================================*/

.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--lamart-shadow-lg);
}

.modal-header {
    background: var(--lamart-gradient);
    color: var(--lamart-white);
    border-radius: 1rem 1rem 0 0;
    border-bottom: none;
}

.modal-header .btn-close {
    filter: invert(1);
}

.modal-footer {
    border-top: 1px solid var(--lamart-border);
    border-radius: 0 0 1rem 1rem;
}

/* =====================================
CHARTS & GRAPHS
========================================*/

.chart-area {
    position: relative;
    height: 20rem;
    width: 100%;
}

.chart-pie {
    position: relative;
    height: 15rem;
    width: 100%;
}

/* =====================================
CUSTOM COMPONENTS
========================================*/

/* File Upload */
.custom-file-label {
    border: 2px solid var(--lamart-border);
    border-radius: 0.5rem;
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

/* Switches */
.custom-switch .custom-control-input:checked ~ .custom-control-label::before {
    background-color: var(--lamart-primary);
    border-color: var(--lamart-primary);
}

/* Select2 Styling */
.select2-container--default .select2-selection--single {
    border: 2px solid var(--lamart-border);
    border-radius: 0.5rem;
    height: calc(1.5em + 0.75rem + 4px);
}

.select2-container--default.select2-container--focus
    .select2-selection--single {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
}

.select2-container--default
    .select2-results__option--highlighted[aria-selected] {
    background-color: var(--lamart-primary);
}

/* =====================================
RESPONSIVE DESIGN
========================================*/

@media (max-width: 768px) {
    .sidebar {
        width: 6.5rem !important;
    }

    .sidebar .nav-item .nav-link span {
        display: none;
    }

    .sidebar-brand-text {
        display: none;
    }

    .card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        border-radius: 0.75rem;
    }
}

/* =====================================
LIGHT THEME ENFORCEMENT
========================================*/

/* Ensure white background throughout */
body {
    background-color: var(--lamart-white) !important;
}

#wrapper {
    background-color: var(--lamart-white) !important;
}

#content-wrapper {
    background-color: var(--lamart-white) !important;
}

.container-fluid {
    background-color: var(--lamart-white) !important;
}

/* Override any dark backgrounds */
.bg-dark {
    background-color: var(--lamart-white) !important;
    color: var(--lamart-text) !important;
}

/* Ensure cards have white background */
.card {
    background-color: var(--lamart-white) !important;
    color: var(--lamart-text) !important;
}

/* Ensure tables have white background */
.table {
    background-color: var(--lamart-white) !important;
    color: var(--lamart-text) !important;
}

/* =====================================
ANIMATIONS & TRANSITIONS
========================================*/

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading Spinner */
.spinner-border-primary {
    color: var(--lamart-primary);
}

/* =====================================
UTILITY CLASSES
========================================*/

.bg-lamart-primary {
    background: var(--lamart-gradient) !important;
}

.bg-lamart-secondary {
    background: var(--lamart-gradient-secondary) !important;
}

.text-lamart-primary {
    color: var(--lamart-primary) !important;
}

.text-lamart-secondary {
    color: var(--lamart-secondary) !important;
}

.border-lamart-primary {
    border-color: var(--lamart-primary) !important;
}

.shadow-lamart {
    box-shadow: var(--lamart-shadow) !important;
}

.shadow-lamart-lg {
    box-shadow: var(--lamart-shadow-lg) !important;
}

/* =====================================
DASHBOARD ENHANCEMENTS
========================================*/

/* Dashboard Stats Cards */
.card.border-left-primary,
.card.border-left-success,
.card.border-left-info,
.card.border-left-warning {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card.border-left-primary::before,
.card.border-left-success::before,
.card.border-left-info::before,
.card.border-left-warning::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
    transition: all 0.3s ease;
}

.card.border-left-primary:hover::before,
.card.border-left-success:hover::before,
.card.border-left-info:hover::before,
.card.border-left-warning:hover::before {
    transform: translate(20px, -20px) scale(1.2);
}

/* Dashboard Card Icons */
.card .fa-2x {
    color: var(--lamart-primary) !important;
    opacity: 0.3;
    transition: all 0.3s ease;
}

.card:hover .fa-2x {
    opacity: 0.6;
    transform: scale(1.1);
}

/* Dashboard Card Text */
.card .text-xs {
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.card .h5 {
    font-size: 2rem;
    font-weight: 800;
    color: var(--lamart-text) !important;
}

/* Dashboard Welcome Section */
.dashboard-welcome {
    background: var(--lamart-gradient);
    color: var(--lamart-white);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.dashboard-welcome::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(50px, -50px);
}

.dashboard-welcome h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.dashboard-welcome p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* =====================================
ENHANCED TABLES
========================================*/

/* Modern Table Styling */
.table-modern {
    background: var(--lamart-white);
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: var(--lamart-shadow);
}

.table-modern thead {
    background: var(--lamart-gradient);
}

.table-modern thead th {
    color: var(--lamart-white);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    border: none;
    padding: 1rem;
}

.table-modern tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid var(--lamart-border);
}

.table-modern tbody tr:hover {
    background: rgba(77, 115, 78, 0.05);
    transform: translateX(5px);
}

.table-modern tbody td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* Status Badges */
.status-badge {
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: rgba(77, 115, 78, 0.1);
    color: var(--lamart-success);
}

.status-badge.inactive {
    background: rgba(220, 53, 69, 0.1);
    color: var(--lamart-danger);
}

.status-badge.pending {
    background: rgba(255, 193, 7, 0.1);
    color: var(--lamart-warning);
}

/* =====================================
ENHANCED FORMS
========================================*/

/* Form Groups */
.form-group-modern {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group-modern label {
    font-weight: 600;
    color: var(--lamart-text);
    margin-bottom: 0.5rem;
    display: block;
}

.form-group-modern .form-control {
    border: 2px solid var(--lamart-border);
    border-radius: 0.75rem;
    padding: 0.75rem 1rem;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-group-modern .form-control:focus {
    border-color: var(--lamart-primary);
    box-shadow: 0 0 0 0.2rem rgba(77, 115, 78, 0.25);
    transform: translateY(-2px);
}

/* File Upload Modern */
.file-upload-modern {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload-modern input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-modern .upload-area {
    border: 2px dashed var(--lamart-border);
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background: var(--lamart-white);
}

.file-upload-modern:hover .upload-area {
    border-color: var(--lamart-primary);
    background: rgba(77, 115, 78, 0.05);
}

.file-upload-modern .upload-icon {
    font-size: 3rem;
    color: var(--lamart-primary);
    margin-bottom: 1rem;
}

.file-upload-modern .upload-text {
    color: var(--lamart-text);
    font-weight: 600;
}

/* =====================================
LOADING STATES
========================================*/

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--lamart-border);
    border-top: 4px solid var(--lamart-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Button Loading State */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid var(--lamart-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}
