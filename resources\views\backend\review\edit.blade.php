@extends('backend.layouts.master')

@section('title','Review Edit')

@section('main-content')
<div class="card">
  <h5 class="card-header">Edit Review</h5>
  <div class="card-body">
    <form action="{{route('review.update',$review->id)}}" method="POST" enctype="multipart/form-data">
      @csrf
      @method('PATCH')

      <div class="form-group">
        <label for="user_id">User:</label>
        <select name="user_id" class="form-control" required>
          <option value="">-- Select User --</option>
          @foreach($users as $user)
            <option value="{{ $user->id }}" {{ $review->user_id == $user->id ? 'selected' : '' }}>
              {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
            </option>
          @endforeach
        </select>
        @error('user_id')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="order_id">Order:</label>
        <select name="order_id" class="form-control" required>
          <option value="">-- Select Order --</option>
          @foreach($orders as $order)
            <option value="{{ $order->id }}" {{ $review->order_id == $order->id ? 'selected' : '' }}>
              Order #{{ $order->order_number }} - {{ $order->first_name }} {{ $order->last_name }}
            </option>
          @endforeach
        </select>
        @error('order_id')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="product_id">Product:</label>
        <select name="product_id" class="form-control" required>
          <option value="">-- Select Product --</option>
          @foreach($products as $product)
            <option value="{{ $product->id }}" {{ $review->product_id == $product->id ? 'selected' : '' }}>
              {{ $product->title }} ({{ $product->price }})
            </option>
          @endforeach
        </select>
        @error('product_id')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="title">Review Title:</label>
        <input type="text" name="title" class="form-control" value="{{ $review->title }}" required>
        @error('title')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="review">Review Content:</label>
        <textarea name="review" cols="20" rows="5" class="form-control" required>{{ $review->review }}</textarea>
        @error('review')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="rate">Rating:</label>
        <select name="rate" class="form-control" required>
          <option value="1" {{ $review->rate == 1 ? 'selected' : '' }}>1 Star</option>
          <option value="2" {{ $review->rate == 2 ? 'selected' : '' }}>2 Stars</option>
          <option value="3" {{ $review->rate == 3 ? 'selected' : '' }}>3 Stars</option>
          <option value="4" {{ $review->rate == 4 ? 'selected' : '' }}>4 Stars</option>
          <option value="5" {{ $review->rate == 5 ? 'selected' : '' }}>5 Stars</option>
        </select>
        @error('rate')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="images">Review Images:</label>
        <div class="mb-3">
          @if($review->images)
            @php $images = json_decode($review->images); @endphp
            @foreach($images as $image)
                @php
                    $image_parts = explode('/', $image);
                    $image_name = array_pop($image_parts);
                @endphp
              <div class="d-inline-block position-relative mr-2 mb-2 image-container">
                <img src="{{ asset($image) }}" width="100" height="100" class="img-thumbnail">
                <a href="{{ route('review.delete.image', ['id' => $review->id, 'image' => $image_name]) }}"
                class="btn btn-sm btn-danger position-absolute delete-image-btn"
                style="top: -10px; right: -10px; border-radius: 50%;">
                    <i class="fa fa-times"></i>
                </a>
              </div>
            @endforeach
          @else
            <p>No images uploaded</p>
          @endif
        </div>
        <input type="file" name="images[]" class="form-control" multiple>
        <small class="text-muted">You can upload multiple images (Max 5MB each)</small>
        @error('images')
            <div class="invalid-feedback d-block">{{ $message }}</div>
        @enderror

        @error('images.*')
            <div class="invalid-feedback d-block">
                @foreach($errors->get('images.*') as $error)
                    {{ implode(' ', $error) }}<br>
                @endforeach
            </div>
        @enderror
      </div>

      <div class="form-group">
        <label for="status">Status:</label>
        <select name="status" class="form-control" required>
          <option value="active" {{ $review->status == 'active' ? 'selected' : '' }}>Active</option>
          <option value="inactive" {{ $review->status == 'inactive' ? 'selected' : '' }}>Inactive</option>
        </select>
        @error('status')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="moderator_notes">Moderator Notes:</label>
        <textarea name="moderator_notes" class="form-control" rows="3">{{ $review->moderator_notes }}</textarea>
        @error('moderator_notes')
            <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <button type="submit" class="btn btn-primary">Update</button>
      <a href="{{ route('review.index') }}" class="btn btn-secondary">Cancel</a>
    </form>
  </div>
</div>
@endsection

@push('styles')
<style>
    .img-thumbnail {
        object-fit: cover;
    }
    .position-relative {
        position: relative;
    }
    .position-absolute {
        position: absolute;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle image deletion via AJAX
        document.querySelectorAll('.delete-image-btn').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('href');

                if (confirm('Are you sure you want to delete this image?')) {
                    fetch(url, {
                        method: 'DELETE',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                            'Accept': 'application/json',
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            this.closest('.image-container').remove();
                        }
                    });
                }
            });
        });
    });
</script>
@endpush
