<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'preferred_method', 'payment_terms', 'save_for_future'
    ];

    protected $casts = [
        'save_for_future' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
