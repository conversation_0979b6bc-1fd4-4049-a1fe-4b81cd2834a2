<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\VisitReminder;
use App\Models\User;
use Illuminate\Http\Request;

class VisitReminderController extends Controller
{
    public function create()
    {
        $salesmen = User::where('role','salesman')->get();
        $customers = User::where('role','customer')->get();
        return view('admin.visit_reminders.create', compact('salesmen','customers'));
    }

    public function store(Request $req)
    {
        $data = $req->validate([
          'salesman_id'=>'required|exists:users,id',
          'customer_id'=>'required|exists:users,id',
          'visit_date'=>'required|date',
          'note'=>'nullable|string',
          'is_recurring'=>'boolean',
        ]);
        VisitReminder::create($data);
        return back()->with('success','Reminder created');
    }
}
