<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Salesman Navigation Map</title>

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
    <!-- Routing Machine CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet-routing-machine/dist/leaflet-routing-machine.css" />

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;
        }
        #map {
            width: 100%;
            height: 100%;
        }
        .info-box {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            font-size: 14px;
        }
        .gmaps-btn {
            position: absolute;
            top: 100px;
            left: 20px;
            z-index: 1000;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
        }
        .directions-box {
            position: absolute;
            top: 160px;
            left: 20px;
            z-index: 1000;
            background: white;
            padding: 10px 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            font-size: 13px;
            width: 250px;
        }
    </style>
</head>
<body>

    <div id="map"></div>

    <div id="route-info" class="info-box">
        Loading route info...
    </div>

    <a id="open-gmaps" class="gmaps-btn" target="_blank">
        Open in Google Maps
    </a>

    <div id="directions" class="directions-box">
        Loading directions...
    </div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <!-- Routing Machine JS -->
    <script src="https://unpkg.com/leaflet-routing-machine/dist/leaflet-routing-machine.js"></script>

    <script>
        const userLat = {{ $user->shipping_lat }};
        const userLng = {{ $user->shipping_long }};

        const map = L.map('map');

// Load tiles
L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap'
}).addTo(map);

// Custom icons
const salesmanIcon = L.icon({
    iconUrl: '{{asset("/images/car-icon.png")}}',
    iconSize: [40, 40],
    iconAnchor: [20, 40]
});

const userIcon = L.icon({
    iconUrl: '{{asset("/images/home-icon.png")}}',
    iconSize: [40, 40],
    iconAnchor: [20, 40]
});

let salesmanMarker;
let routingControl;

if (navigator.geolocation) {
    navigator.geolocation.watchPosition(function(position) {
        const salesmanLat = position.coords.latitude;
        const salesmanLng = position.coords.longitude;

        if (!salesmanMarker) {
            // FIRST time we get salesman's location → center the map here
            map.setView([salesmanLat, salesmanLng], 13);

            salesmanMarker = L.marker([salesmanLat, salesmanLng], {icon: salesmanIcon})
                .addTo(map)
                .bindPopup('You are here')
                .openPopup();

            // User marker
            L.marker([userLat, userLng], {icon: userIcon})
                .addTo(map)
                .bindPopup('User location');

            // Routing
            routingControl = L.Routing.control({
                waypoints: [
                    L.latLng(salesmanLat, salesmanLng),
                    L.latLng(userLat, userLng)
                ],
                routeWhileDragging: true,
                show: false
            }).addTo(map);

            routingControl.on('routesfound', function(e) {
                const routes = e.routes;
                const summary = routes[0].summary;

                const distanceKm = (summary.totalDistance / 1000).toFixed(2);
                const timeMin = Math.round(summary.totalTime / 60);

                document.getElementById('route-info').innerHTML = `
                    Distance: ${distanceKm} km<br>
                    Estimated Time: ${timeMin} minutes
                `;

                let directionsHtml = '<h4>Directions:</h4><ol>';
                routes[0].instructions.forEach(function(step) {
                    directionsHtml += `<li>${step.text}</li>`;
                });
                directionsHtml += '</ol>';

                document.getElementById('directions').innerHTML = directionsHtml;
            });

            document.getElementById('open-gmaps').href = `https://www.google.com/maps/dir/?api=1&destination=${userLat},${userLng}`;

        } else {
            // Update salesman location live
            salesmanMarker.setLatLng([salesmanLat, salesmanLng]);
            routingControl.spliceWaypoints(0, 1, L.latLng(salesmanLat, salesmanLng));
        }

    }, function(error) {
        alert('Error getting location: ' + error.message);
    }, {
        enableHighAccuracy: true
    });
} else {
    alert('Geolocation is not supported by your browser.');
}
    </script>

</body>
</html>
