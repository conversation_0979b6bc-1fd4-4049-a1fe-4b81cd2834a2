<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\SendVisitReminders::class,
        Commands\SendPromotionalEmails::class,
        Commands\TestEmailSystem::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Send visit reminders daily at 8:00 AM (1 day before visit)
        $schedule->command('visits:send-reminders --days=1')
            ->dailyAt('08:00')
            ->withoutOverlapping()
            ->runInBackground()
            ->emailOutputOnFailure(env('ADMIN_EMAIL', '<EMAIL>'));

        // Send weekly promotional emails on Mondays at 10:00 AM
        // $schedule->command('email:send-promotional --type=general')
        //     ->weeklyOn(1, '10:00')
        //     ->withoutOverlapping()
        //     ->runInBackground()
        //     ->emailOutputOnFailure(env('ADMIN_EMAIL', '<EMAIL>'));

        // Clean up failed jobs older than 7 days
        $schedule->command('queue:prune-failed --hours=168')
            ->daily()
            ->runInBackground();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
