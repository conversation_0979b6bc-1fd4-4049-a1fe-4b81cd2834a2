@extends('backend.layouts.master')

@section('title', 'Edit Visit Reminder')

@section('main-content')
<div class="card shadow mb-4">
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary">Edit Visit Reminder</h6>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ route('salesman.visit.update', $reminder->id) }}">
            @csrf
            @method('PUT')

            <div class="form-group">
                <label for="customer_id">Customer</label>
                <select name="customer_id" id="customer_id" class="form-control" required>
                    <option value="">Select Customer</option>
                    @foreach($customers as $customer)
                        <option value="{{ $customer->id }}" {{ $reminder->customer_id == $customer->id ? 'selected' : '' }}>
                            {{ $customer->first_name }} {{ $customer->last_name }}
                        </option>
                    @endforeach
                </select>
                @error('customer_id')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="visit_date">Visit Date</label>
                <input type="datetime-local" name="visit_date" id="visit_date" value="{{ old('visit_date', $reminder->visit_date) }}" class="form-control" required>
                @error('visit_date')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="note">Note (Optional)</label>
                <textarea name="note" id="note" rows="4" class="form-control">{{ old('note', $reminder->note) }}</textarea>
                @error('note')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <label class="inline-flex items-center ml-4">
                    <input type="checkbox" name="is_recurring" id="is_recurring" value="1" class="form-check-input" {{ old('is_recurring', $reminder->is_recurring) ? 'checked' : '' }}>
                    <span class="ml-2">Is Recurring?</span>
                </label>
                @error('is_recurring')
                    <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="recurring_options d-none">
                <div class="form-group">
                    <label for="interval">Interval</label>
                    <input type="number" name="interval" id="interval" value="{{ old('interval', $reminder->interval) }}" class="form-control">
                    @error('interval')
                        <span class="text-danger">{{ $message }}</span>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="frequency" class="form-label fw-medium required">Frequency</label>
                    <select name="frequency" id="frequency" class="form-control py-1 px-2 rounded-2" required>
                        <option value="" {{ old('frequency', $reminder->frequency) === '' ? 'selected' : '' }}>Select Frequency</option>
                        <option value="day" {{ old('frequency', $reminder->frequency) === 'day' ? 'selected' : '' }}>Day</option>
                        <option value="week" {{ old('frequency', $reminder->frequency) === 'week' ? 'selected' : '' }}>Week</option>
                        <option value="month" {{ old('frequency', $reminder->frequency) === 'month' ? 'selected' : '' }}>Month</option>
                        <option value="year" {{ old('frequency', $reminder->frequency) === 'year' ? 'selected' : '' }}>Year</option>
                    </select>
                    @error('frequency')
                        <span class="text-danger small">{{ $message }}</span>
                    @enderror
                </div>
            </div>

            <button type="submit" class="btn btn-primary">Update Reminder</button>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#is_recurring').change(function() {
            if ($(this).is(':checked')) {
                $('.recurring_options').removeClass('d-none');
            } else {
                $('.recurring_options').addClass('d-none');
            }
        });

        $('#is_recurring').trigger('change');
    });
</script>
@endpush
