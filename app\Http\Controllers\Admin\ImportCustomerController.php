<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Imports\CustomerImport;
use Maatwebsite\Excel\Facades\Excel;

class ImportCustomerController extends Controller
{
    public function showForm()
    {
        return view('backend.import.customer');
    }

    public function importCustomers(Request $request)
    {
        $request->validate([
            'csv' => 'required|mimes:csv,txt',
        ]);

        Excel::import(new CustomerImport, $request->file('csv'));

        return redirect()->back()->with('success', 'Data imported successfully!');
    }
}
