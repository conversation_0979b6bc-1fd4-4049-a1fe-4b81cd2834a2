<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Product;
use Notification;
use App\Notifications\StatusNotification;
use App\Models\User;
use App\Models\ProductReview;
use App\Models\Order;
use Illuminate\Support\Facades\Storage;

class ProductReviewController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $reviews=ProductReview::getAllReview();

        return view('backend.review.index')->with('reviews',$reviews);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $orders = Order::all();
        $users = User::all();
        $products = Product::all();
        return view('backend.review.create', compact('orders', 'users', 'products'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'order_id' => 'required|exists:orders,id',
            'product_id' => 'required|exists:products,id',
            'title' => 'required|string|max:255',
            'review' => 'required|string',
            'rate' => 'required|integer|between:1,5',
            'status' => 'required|in:active,inactive',
            'images' => 'nullable',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $data = $request->except('images');

        // Handle image uploads
        if($request->hasFile('images')){
            $images = [];
            foreach($request->file('images') as $image){
                $imageName = time().'_'.$image->getClientOriginalName();
                $image->move(public_path('images/reviews'), $imageName);
                $images[] = 'images/reviews/'.$imageName;
            }
            $data['images'] = json_encode($images);
        }

        $status = ProductReview::create($data);
        if($status){
            request()->session()->flash('success','Review created successfully');
        }
        else{
            request()->session()->flash('error','Something went wrong! Please try again!!');
        }
        return redirect()->route('review.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $review=ProductReview::find($id);
        $orders = Order::all();
        $users = User::all();
        $products = Product::all();
        // return $review;
        return view('backend.review.edit', compact('orders', 'users', 'products', 'review'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'order_id' => 'required|exists:orders,id',
            'product_id' => 'required|exists:products,id',
            'title' => 'required|string|max:255',
            'review' => 'required|string',
            'rate' => 'required|integer|between:1,5',
            'status' => 'required|in:active,inactive',
            'images' => 'nullable',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:5120',
        ]);

        $review=ProductReview::find($id);
        if($review){
            $review = ProductReview::find($id);

            $data = $request->except('images');

            if($request->hasFile('images')){
                $images = $review->images ? json_decode($review->images) : [];
                foreach($request->file('images') as $image){
                    $imageName = time().'_'.$image->getClientOriginalName();
                    $image->move(public_path('images/reviews'), $imageName);
                    $images[] = 'images/reviews/'.$imageName;
                }
                $data['images'] = json_encode($images);
            }

            $review->fill($data);
            $review->save();
            if($review){
                request()->session()->flash('success','Review Successfully updated');
            }
            else{
                request()->session()->flash('error','Something went wrong! Please try again!!');
            }
        }
        else{
            request()->session()->flash('error','Review not found!!');
        }

        return redirect()->route('review.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $review=ProductReview::find($id);
        $status=$review->delete();
        if($status){
            request()->session()->flash('success','Successfully deleted review');
        }
        else{
            request()->session()->flash('error','Something went wrong! Try again');
        }
        return redirect()->route('review.index');
    }

    public function deleteImage(Request $request, $id, $image)
    {
        $review = ProductReview::findOrFail($id);

        $image = 'images/reviews/' . $image;
        $images = json_decode($review->images, true);
        $images = array_filter($images, fn($img) => $img !== $image);

        if (Storage::exists($image)) {
            Storage::delete($image);
        }

        $review->images = json_encode(array_values($images));
        $review->save();

        return response()->json(['success' => true]);
    }
}
