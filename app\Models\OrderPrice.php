<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderPrice extends Model
{
    use HasFactory;

    protected $table = 'order_prices';

    protected $fillable = ['item_color_id', 'order_id', 'price', 'is_one_time'];

    public function itemColor()
    {
        return $this->belongsTo(ItemColor::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
