<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\VisitReminder;

class VisitReminderNotification extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    protected $reminder;
    public function __construct(VisitReminder $r){ $this->reminder = $r; }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database','mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Upcoming Visit Reminder')
            ->line("You have a visit scheduled with {$this->reminder->customer->first_name} on {$this->reminder->visit_date->format('Y-m-d H:i')}.")
            ->action('View Customer', url("/salesman/customers/{$this->reminder->customer_id}"));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable)
    {
        return [
            'reminder_id'=>$this->reminder->id,
            'message'=>"Visit with {$this->reminder->customer->first_name} on {$this->reminder->visit_date->format('Y-m-d H:i')}."
        ];
    }
}
