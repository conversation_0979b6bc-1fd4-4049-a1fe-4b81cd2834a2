@extends('backend.layouts.master')

@section('main-content')
<div class="container-fluid py-4">
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Edit Order</h5>
            <a href="{{ route('salesman.return.create', $order->id) }}" class="btn btn-sm btn-light shadow-sm">
                Create Return
            </a>
        </div>
        <div class="card-body p-4">
            <form method="post" action="{{ route('salesman.orders.update', $order->id) }}" enctype="multipart/form-data" id="edit-order">
                @csrf
                @method('PUT')

                {{-- Customer --}}
                <div class="border rounded p-3 mb-3">
                    <label for="user_id" class="form-label fw-semibold">Select Customer <span class="text-danger">*</span></label>
                    <select name="user_id" id="user_id" class="form-select select2" required>
                        <option value="">-- Select Customer --</option>
                    </select>
                    @error('user_id')
                        <div class="invalid-feedback d-block">{{ $message }}</div>
                    @enderror
                </div>

                {{-- Shipping Address Display --}}
                <div class="border rounded p-3 mb-3">
                    <label class="form-label fw-semibold">Shipping Address</label>
                    <textarea name="address1" id="shipping-address" class="form-control" readonly>{{ $order->address1 }}</textarea>
                    <input type="hidden" name="shipping_price" id="shipping-price" value="{{ $order->shipping_price ?? 0 }}">
                </div>

                {{-- Delivery Method --}}
                <div class="border rounded p-3 mb-3">
                    <label for="delivery_method" class="form-label fw-semibold">Delivery Method <span class="text-danger">*</span></label>
                    <select name="delivery_method" id="delivery_method" class="form-select" required>
                        <option value="pickup" {{ $order->delivery_method == 'pickup' ? 'selected' : '' }}>Pickup</option>
                        <option value="ship" {{ $order->delivery_method == 'ship' ? 'selected' : '' }}>Ship</option>
                        <option value="delivery" {{ $order->delivery_method == 'delivery' ? 'selected' : '' }}>Delivery</option>
                        <option value="instant" {{ $order->delivery_method == 'instant' ? 'selected' : '' }}>Instant Delivery</option>
                    </select>
                </div>

                {{-- Checkboxes --}}
                <div class="border rounded p-3 mb-3">
                    <div class="form-check mb-2">
                        <input type="checkbox" name="safe_for_future" class="form-check-input" id="safe_for_future" {{ $order->safe_for_future ? 'checked' : '' }}>
                        <label for="safe_for_future" class="form-check-label">Save for Future</label>
                    </div>
                    <div class="form-check mb-2" id="is-paid-section" style="display:{{ $order->delivery_method == 'instant' ? 'block' : 'none' }};">
                        <input type="checkbox" name="is_paid" class="form-check-input" id="is_paid" {{ $order->is_paid ? 'checked' : '' }}>
                        <label for="is_paid" class="form-check-label">Mark as Paid</label>
                    </div>
                    <div class="form-check">
                        <input type="checkbox" name="quick_order" class="form-check-input" id="quick_order">
                        <label for="quick_order" class="form-check-label">Quick Order</label>
                    </div>
                </div>

                <!-- Quick Order Panel (hidden by default) -->
                <div class="row mb-4" id="quick_order_panel" style="display:none;">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6>Select from Previous Orders</h6>
                                <button type="button" class="btn btn-sm btn-primary" id="addSelectedOrders">Add Selected to Order</button>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm" id="previousOrdersTable">
                                    <thead>
                                        <tr>
                                            <th>Select</th>
                                            <th>Order Date</th>
                                            <th>Items</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Signature (hidden by default for non-instant) --}}
                <div class="border rounded p-3 mb-3" id="signature-section" style="display:{{ $order->delivery_method == 'instant' ? 'block' : 'none' }};">
                    <label class="form-label fw-semibold">Customer Signature <span class="text-danger">*</span></label>
                    <div class="border rounded bg-light p-2">
                        <canvas id="signature-pad" class="rounded" width="400" height="200"></canvas>
                    </div>
                    <input type="hidden" name="signature_data" id="signature_data" value="{{ $order->signature_data }}">
                    <button type="button" class="btn btn-outline-danger btn-sm mt-2" id="clear-signature">Clear Signature</button>
                </div>

                {{-- Order Items Table --}}
                <div class="border rounded p-3 mb-3">
                    <h5 class="fw-semibold mb-3">Order Items</h5>
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Original Price</th>
                                <th>Custom Price</th>
                                <th>Save Custom Price</th>
                                <th>Qty</th>
                                <th>Total</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="items">
                            @foreach($order->items as $index => $item)
                                <tr class="item-row">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <select name="items[{{ $index }}][product_id]" class="form-select select2 product_dropdown" required>
                                                <option value="">-- Select Product --</option>
                                                <!-- Options will be populated dynamically via JavaScript -->
                                            </select>
                                            <img src="{{ $item->product->item_colors->where('color_id', $item->color)->first()->photo ? asset('storage/' . $item->product->item_colors->where('color_id', $item->color)->first()->photo) : '' }}" style="width:35px; height:35px; object-fit:cover; margin-left:10px;" />
                                        </div>
                                        <input name="items[{{ $index }}][color]" type="hidden" class="form-control" value="{{ $item->color }}" data-product-id="{{ $item->product_id }}">
                                    </td>
                                    <td>
                                        <input name="items[{{ $index }}][original_price]" type="number" step="0.01" class="form-control original-price" value="{{ $item->original_price }}" readonly>
                                    </td>
                                    <td>
                                        <input name="items[{{ $index }}][price]" type="number" step="0.01" class="form-control price" value="{{ $item->price }}" readonly>
                                    </td>
                                    <td>
                                        <input type="checkbox" name="items[{{ $index }}][use_custom_price]" class="form-check-input use-custom-price ml-3">
                                        <input type="hidden" name="items[{{ $index }}][is_one_time]" value="1">
                                    </td>
                                    <td>
                                        <input name="items[{{ $index }}][quantity]" type="number" min="1" value="{{ $item->quantity }}" class="form-control quantity">
                                    </td>
                                    <td>
                                        <span class="item-total">{{ number_format($item->price * $item->quantity, 2) }}</span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-outline-danger remove-item">Remove</button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <button type="button" id="add-item" class="btn btn-outline-primary mt-2">+ Add Item</button>

                    {{-- Summary Section --}}
                    <table class="table table-bordered mt-3">
                        <tr>
                            <td>Subtotal</td>
                            <td>$<span id="subtotal">0.00</span></td>
                        </tr>
                        <tr>
                            <td>Shipping</td>
                            <td>$<span id="shipping-cost">0.00</span></td>
                        </tr>
                        <tr>
                            <td><strong>Total</strong></td>
                            <td><strong>$<span id="total-price">0.00</span></strong></td>
                        </tr>
                    </table>
                </div>

                {{-- Submit --}}
                <div class="d-flex gap-3">
                    <button type="reset" class="btn btn-outline-warning">Reset</button>
                    <button type="submit" id="submit-order-btn" class="btn btn-success ml-3">Update Order</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div class="border rounded p-3 mb-3">
    <h5 class="fw-semibold mb-3">Order History</h5>
    <table class="table table-bordered">
        <thead>
            <tr>
                <th>Date</th>
                <th>Action By</th>
                <th>Customer Name</th>
                <th>Action</th>
                <th>Details</th>
            </tr>
        </thead>
        <tbody>
            @foreach($order->histories as $history)
                <tr>
                    <td>{{ $history->created_at->format('M d, Y H:i') }}</td>
                    <td>{{ @$history->user->first_name }} {{ @$history->user->last_name }}</td>
                    <td>{{ @$history->customer->first_name }} {{ @$history->customer->last_name }}</td>
                    <td>{{ str_replace('_', ' ', ucfirst($history->action)) }}</td>
                    <td>
                        @if($history->action === 'added_item')
                            Added product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}) with quantity {{ $history->details['quantity'] }} at ${{ $history->details['price'] }}
                        @elseif($history->action === 'updated_item')
                            Updated product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}): Quantity from {{ $history->details['old_quantity'] }} to {{ $history->details['new_quantity'] }}, Price from ${{ $history->details['old_price'] }} to ${{ $history->details['new_price'] }}
                        @elseif($history->action === 'removed_item')
                            Removed product ID {{ $history->details['product_id'] }} (Color: {{ $history->details['color'] }}) with quantity {{ $history->details['quantity'] }} at ${{ $history->details['price'] }}
                        @elseif($history->action === 'edited_order')
                            Edited order details
                        @endif
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="orderConfirmationModal" tabindex="-1" aria-labelledby="orderConfirmationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderConfirmationModalLabel">Confirm Order Details</h5>
                <button type="button" class="btn-close" data-dismiss="modal" aria-label="Close">x</button>
            </div>
            <div class="modal-body">
                <h6>Customer Information</h6>
                <p><strong>Customer:</strong> <span id="modal-customer"></span></p>
                <p><strong>Shipping Address:</strong> <span id="modal-shipping-address"></span></p>
                <p><strong>Delivery Method:</strong> <span id="modal-delivery-method"></span></p>
                <p><strong>Save for Future:</strong> <span id="modal-save-future"></span></p>
                <p><strong>Mark as Paid:</strong> <span id="modal-is-paid"></span></p>
                <p><strong>Quick Order:</strong> <span id="modal-quick-order"></span></p>

                <h6 class="mt-4">Order Items</h6>
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th>Original Price</th>
                            <th>Custom Price</th>
                            <th>Quantity</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody id="modal-items">
                    </tbody>
                </table>

                <h6 class="mt-4">Summary</h6>
                <table class="table table-bordered">
                    <tr>
                        <td>Subtotal</td>
                        <td>$<span id="modal-subtotal">0.00</span></td>
                    </tr>
                    <tr>
                        <td>Shipping</td>
                        <td>$<span id="modal-shipping-cost">0.00</span></td>
                    </tr>
                    <tr>
                        <td><strong>Total</strong></td>
                        <td><strong>$<span id="modal-total-price">0.00</span></strong></td>
                    </tr>
                </table>

                <div id="modal-signature-section" style="display:none;">
                    <h6>Customer Signature</h6>
                    <img id="modal-signature" class="img-fluid" style="max-width: 400px;" />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" id="confirm-order-btn">Confirm Order</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
<style>
    .select2-container--default {
        width: 100% !important;
    }
    .select2-container--bootstrap-5,
    .select2-container--default .select2-selection--single {
        border: 1px solid #ced4da !important;
        border-radius: 0.375rem;
        height: 38px;
        padding: 0.375rem 0.75rem;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    .select2-container--default .select2-selection--single:focus {
        border-color: #86b7fe !important;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        outline: 0;
    }
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        line-height: 22px;
        color: #495057;
    }
    .select2-container--default .select2-selection--single .select2-selection__arrow {
        height: 38px;
    }
    .select2-container--default .select2-results__option {
        padding: 6px 12px;
    }
    .select2-container .select2-selection--single .select2-selection__rendered {
        padding-left: 0;
    }
    canvas {
        max-width: 100%;
        height: auto;
    }
    .table th, .table td {
        vertical-align: middle;
    }
    .item-total {
        display: inline-block;
        width: 100%;
        text-align: center;
    }
    .mr-3 {
        margin-right: 1rem;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.6/dist/signature_pad.umd.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
    $(document).ready(function(){
        let canvas = document.getElementById('signature-pad');
        let signaturePad = new SignaturePad(canvas);

        function resizeCanvas() {
            let ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext('2d').scale(ratio, ratio);
            signaturePad.clear();
            let signatureData = $('#signature_data').val();
            if (signatureData) {
                signaturePad.fromDataURL(signatureData);
            }
        }
        window.addEventListener('resize', resizeCanvas);
        resizeCanvas();

        document.getElementById('clear-signature').addEventListener('click', function () {
            signaturePad.clear();
        });

        $('.select2').select2({
            theme: 'bootstrap-5',
            width: '100%'
        });

        let productOptions = [];
        window.productStock = {};

        function updateProductOptions(userId) {
            $.ajax({
                url: '{{ route("salesman.products.prices") }}',
                type: 'GET',
                data: { user_id: userId },
                success: function (res) {
                    productOptions = [];
                    window.productStock = {};
                    res.forEach(product => {
                        product.item_colors.forEach(itemColor => {
                            window.productStock[product.id] = window.productStock[product.id] || {};
                            window.productStock[product.id][itemColor.color.id] = itemColor.stock;
                            const option = `
                                <option
                                    value="${product.id}"
                                    data-item-number="${itemColor.item_number}"
                                    data-item-name="${product.title}"
                                    data-price="${itemColor.prices.custom_price || itemColor.prices.original_price}"
                                    data-original-price="${itemColor.prices.original_price}"
                                    data-custom-price="${itemColor.prices.custom_price || ''}"
                                    data-color="${itemColor.color.id}"
                                    data-photo="${itemColor.photo ? '{{ asset('storage') }}/' + itemColor.photo : ''}"
                                >
                                    ${itemColor.item_number} - ${product.title} (${itemColor.color.name})
                                </option>`;
                            productOptions.push(option);
                        });
                    });
                    updateAllProductDropdowns();
                },
                error: function () {
                    Swal.fire({
                        title: 'Error!',
                        text: 'Failed to load product prices.',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                }
            });
        }

        function updateAllProductDropdowns() {
            $('#items .item-row').each(function() {
                const selectElement = $(this).find('select[name*="[product_id]"]');
                const currentValue = $(this).find('input[name*="[color]"]').data('product-id') || selectElement.val();
                const currentColor = $(this).find('input[name*="[color]"]').val();
                const originalPrice = parseFloat($(this).find('input[name*="[original_price]"]').val()) || 0;
                const price = parseFloat($(this).find('input[name*="[price]"]').val()) || 0;
                const useCustomPrice = $(this).find('.use-custom-price').is(':checked');
                let matched;
                // Destroy existing Select2 instance
                if (selectElement.hasClass('select2-hidden-accessible')) {
                    selectElement.select2('destroy');
                }

                selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));

                if (currentValue && currentColor) {
                    matched = false;
                    selectElement.find('option').each(function() {
                        if ($(this).val() == currentValue && $(this).data('color') == currentColor) {
                            selectElement.val(currentValue);
                            matched = true;
                            return false;
                        }
                    });
                    if (!matched) {
                        selectElement.val('');
                    }
                } else {
                    selectElement.val('');
                }

                initSelect2(selectElement);
                if (currentValue && matched) {
                    updateHiddenFields(selectElement, originalPrice, price, useCustomPrice);
                    selectElement.trigger('change');
                } else {
                    // Reset fields if no match
                    $(this).find('input[name*="[price]"]').val('');
                    $(this).find('input[name*="[original_price]"]').val('');
                    $(this).find('.use-custom-price').prop('checked', false);
                    $(this).find('input[name*="[is_one_time]"]').val('1');
                    updateItemTotal($(this));
                    calculateTotalPrice();
                }
            });
        }

        function updateHiddenFields(selectElement, originalPrice = null, price = null, useCustomPrice = false) {
            const selectedOption = selectElement.find('option:selected');
            const row = selectElement.closest('.item-row');
            const defaultPrice = parseFloat(selectedOption.data('price')) || 0;
            const defaultOriginalPrice = parseFloat(selectedOption.data('original-price')) || 0;
            const customPrice = selectedOption.data('custom-price') ? parseFloat(selectedOption.data('custom-price')) : null;

            // Use stored price if available, else fallback to custom_price or original_price
            row.find('input[name*="[original_price]"]').val(defaultOriginalPrice.toFixed(2));
            row.find('input[name*="[price]"]').val((price !== null ? price : (useCustomPrice && customPrice ? customPrice : defaultPrice)).toFixed(2));
            row.find('input[name*="[color]"]').val(selectedOption.data('color') || '');
            row.find('img').attr('src', selectedOption.data('photo') || '');
            row.find('.use-custom-price').prop('checked', useCustomPrice || (customPrice && price === customPrice));
            row.find('input[name*="[is_one_time]"]').val((useCustomPrice || (customPrice && price === customPrice)) ? '0' : '1');
            updateItemTotal(row);
            calculateTotalPrice();
        }

        function initSelect2(element) {
            element.select2({
                theme: 'bootstrap-5',
                width: '100%',
                templateResult: formatProductOption,
                templateSelection: formatProductSelection
            }).on('change', function() {
                updateHiddenFields($(this));
                validateStock($(this).closest('.item-row'));
                validateAllStock();
            });
        }

        function formatProductOption(option) {
            if (!option.id) return option.text;
            var photo = $(option.element).data('photo');
            var $option = $('<div class="d-flex align-items-center">' +
                (photo ? '<img src="' + photo + '" class="mr-3" style="width:35px; height:35px; object-fit:cover;">' : '') +
                '<div>' + option.text + '</div></div>');
            return $option;
        }

        function formatProductSelection(option) {
            if (!option.id) return option.text;
            var photo = $(option.element).data('photo');
            var $selection = $('<div class="d-flex align-items-center">' +
                (photo ? '<img src="' + photo + '" class="mr-3" style="width:35px; height:35px; object-fit:cover;">' : '') +
                '<div>' + option.text + '</div></div>');
            return $selection;
        }

        $('.product_dropdown').each(function() {
            initSelect2($(this));
        });

        function loadCustomers(salesmanId, selectedCustomerId) {
            $('#user_id').html('<option>Loading...</option>').trigger('change');
            $.ajax({
                url: "{{ route('salesman.customers', ['id' => '__ID__']) }}".replace('__ID__', salesmanId),
                type: 'GET',
                success: function (res) {
                    let options = '<option value="">-- Select Customer --</option>';
                    res.forEach(customer => {
                        let selected = customer.id == selectedCustomerId ? 'selected' : '';
                        options += `<option value="${customer.id}" ${selected} data-address="${customer.shipping_address} ${customer.city} ${customer.shipping_state} ${customer.shipping_zip}" data-state="${customer.shipping_state}">${customer.first_name} ${customer.last_name} (${customer.email}) - ${customer.shipping_address} ${customer.city} ${customer.shipping_state} ${customer.shipping_zip}</option>`;
                    });
                    $('#user_id').html(options).trigger('change');
                }
            });
        }

        let previousOrdersTable = $('#previousOrdersTable').DataTable({
            pageLength: 5,
            lengthMenu: [5, 10, 25, 50],
            searching: true,
            ordering: true,
            info: true,
            paging: true,
            language: {
                search: "Search orders:",
                emptyTable: "No previous orders found."
            },
            columnDefs: [
                { orderable: false, targets: 0 }
            ]
        });

        function loadPreviousOrders(customerId) {
            $.ajax({
                url: "{{ route('salesman.customer.orders', ['id' => '__ID__']) }}".replace('__ID__', customerId),
                type: 'GET',
                success: function (res) {
                    previousOrdersTable.clear();
                    if (res.length === 0) {
                        previousOrdersTable.draw();
                        return;
                    }
                    res.forEach(order => {
                        if (order.id === {{ $order->id }}) return;
                        let itemsList = '';
                        let itemsData = [];
                        order.items.forEach(item => {
                            itemsList += `<li>${item.quantity} x ${item.product.title} (${item.color})</li>`;
                            itemsData.push({
                                product_id: item.product_id,
                                quantity: item.quantity,
                                price: item.price,
                                color: item.color,
                                total: (item.price * item.quantity).toFixed(2)
                            });
                        });
                        previousOrdersTable.row.add([
                            `<input type="checkbox" name="previous_orders[]" data-items='${JSON.stringify(itemsData)}'>`,
                            new Date(order.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' }),
                            `<ul class="list-unstyled">${itemsList}</ul>`,
                            `$${parseFloat(order.total_amount).toFixed(2)}`
                        ]);
                    });
                    previousOrdersTable.draw();
                },
                error: function () {
                    previousOrdersTable.clear().draw();
                }
            });
        }

        let initialSalesmanId = {{ $order->salesman_id }};
        let initialCustomerId = {{ $order->user_id }};
        if (initialSalesmanId) {
            loadCustomers(initialSalesmanId, initialCustomerId);
            updateProductOptions(initialCustomerId);
        }

        $('#user_id').on('change', function () {
            const selectedOption = $(this).find('option:selected');
            const address = selectedOption.data('address') || '';
            const state = selectedOption.data('state') || '';
            const customerId = $(this).val();
            $('#shipping-address').val(address);
            calculateShipping(address, state);
            calculateTotalPrice();
            if (customerId) {
                updateProductOptions(customerId);
                if ($('#quick_order').is(':checked')) {
                    loadPreviousOrders(customerId);
                }
            } else {
                previousOrdersTable.clear().draw();
                productOptions = [];
                updateAllProductDropdowns();
            }
        });

        function calculateShipping(address, state) {
            let shippingCost = 0;
            const deliveryMethod = $('#delivery_method').val();
            if (deliveryMethod === 'pickup' || deliveryMethod === 'instant') {
                shippingCost = 0;
            } else if (deliveryMethod === 'ship') {
                if (state) {
                    const stateCosts = {
                        'CA': 10.00,
                        'NY': 15.00,
                        'TX': 12.00,
                    };
                    shippingCost = stateCosts[state.toUpperCase()] || 5.00;
                } else if (address) {
                    shippingCost = address.length > 50 ? 8.00 : 5.00;
                }
            }
            $('#shipping-price').val(shippingCost.toFixed(2));
            $('#shipping-cost').text(shippingCost.toFixed(2));
        }

        $('#delivery_method').on('change', function () {
            const deliveryMethod = $(this).val();
            const selectedOption = $('#user_id').find('option:selected');
            const address = selectedOption.data('address') || '';
            const state = selectedOption.data('state') || '';
            $('#signature-section').toggle(deliveryMethod === 'instant');
            $('#is-paid-section').toggle(deliveryMethod === 'instant');
            calculateShipping(address, state);
            calculateTotalPrice();
        });

        let itemIndex = {{ $order->items->count() }};

        $('#add-item').click(function() {
            let row = $('#items .item-row').first().clone();
            row.find('select, input').each(function() {
                const oldName = $(this).attr('name');
                if (oldName) {
                    const newName = oldName.replace(/\[\d+\]/, `[${itemIndex}]`);
                    $(this).attr('name', newName).val(
                        oldName.includes('quantity') ? '1' :
                        oldName.includes('price') || oldName.includes('original_price') ? '' :
                        oldName.includes('is_one_time') ? '1' : ''
                    );
                }
            });
            row.find('.use-custom-price').prop('checked', false);
            row.find('.item-total').text('0.00');
            let selectElement = row.find('select[name^="items"]');
            selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
            selectElement.val('');
            selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
            row.find('.stock-feedback').remove();
            row.find('img').attr('src', '');
            $('#items').append(row);
            initSelect2(selectElement);
            itemIndex++;
            calculateTotalPrice();
        });

        $(document).on('click', '.remove-item', function () {
            if ($('#items .item-row').length > 1) {
                $(this).closest('.item-row').remove();
                validateAllStock();
                calculateTotalPrice();
            }
        });

        $(document).on('change', '.use-custom-price', function() {
            const row = $(this).closest('.item-row');
            const isChecked = $(this).is(':checked');
            const selectElement = row.find('select[name*="[product_id]"]');
            const selectedOption = selectElement.find('option:selected');
            const customPrice = selectedOption.data('custom-price') ? parseFloat(selectedOption.data('custom-price')) : null;

            row.find('input[name*="[is_one_time]"]').val(isChecked ? '0' : '1');
            row.find('input[name*="[price]"]').val(customPrice);
            row.find('input[name*="[price]"]').prop('readonly', !isChecked);
            updateItemTotal(row);
            calculateTotalPrice();
        });

        function validateStock(row) {
            const selectElement = row.find('select[name*="[product_id]"]');
            const productId = selectElement.val();
            const selectedOption = selectElement.find('option:selected');
            const quantityInput = row.find('input[name*="[quantity]"]');
            const quantity = parseInt(quantityInput.val()) || 0;
            const colorId = row.find('input[name*="[color]"]').val(selectedOption.data('color') || '');
            row.find('.stock-feedback').remove();

            if (!productId || isNaN(quantity) || quantity <= 0) return true;

            const available = parseInt(window.productStock[productId][colorId.val()]) || 0;
            if (quantity > available) {
                const message = `<small class="invalid-feedback d-block stock-feedback">Only ${available} in stock</small>`;
                quantityInput.after(message);
                quantityInput.val(available);
                updateItemTotal(row);
                calculateTotalPrice();
                return false;
            }
            quantityInput.focus();
            return true;
        }

        function validateAllStock() {
            let valid = true;
            $('#items .item-row').each(function () {
                const rowValid = validateStock($(this));
                if (!rowValid) valid = false;
            });
            $('#submit-order-btn').prop('disabled', !valid);
        }

        function updateItemTotal(row) {
            const price = parseFloat(row.find('input[name*="[price]"]').val()) || 0;
            const quantity = parseInt(row.find('input[name*="[quantity]"]').val()) || 0;
            const total = price * quantity;
            row.find('.item-total').text(total.toFixed(2));
        }

        function calculateTotalPrice() {
            let subtotal = 0;
            $('#items .item-row').each(function() {
                const price = parseFloat($(this).find('input[name*="[price]"]').val()) || 0;
                const quantity = parseInt($(this).find('input[name*="[quantity]"]').val()) || 0;
                subtotal += price * quantity;
            });
            const shippingCost = parseFloat($('#shipping-price').val()) || 0;
            const total = subtotal + shippingCost;
            $('#subtotal').text(subtotal.toFixed(2));
            $('#shipping-cost').text(shippingCost.toFixed(2));
            $('#total-price').text(total.toFixed(2));
        }

        $(document).on('blur', 'input[name*="[quantity]"]', function () {
            const row = $(this).closest('.item-row');
            // let quantity = parseInt($(this).val()) || 0;
            // if (quantity === 0) {
            //     $(this).val(1);
            //     quantity = 1;
            // }
            // const roundedQuantity = Math.round(quantity / 12) * 12;
            // if (quantity !== roundedQuantity) {
            //     Swal.fire({
            //         title: 'Round to nearest multiple of 12?',
            //         text: `The entered quantity (${quantity}) will be rounded to ${roundedQuantity}. Proceed?`,
            //         icon: 'question',
            //         showCancelButton: true,
            //         confirmButtonColor: '#3085d6',
            //         cancelButtonColor: '#d33',
            //         confirmButtonText: 'Yes, round it!'
            //     }).then((result) => {
            //         if (result.isConfirmed) {
            //             $(this).val(roundedQuantity);
            //         }
            //         validateStock(row);
            //         validateAllStock();
            //         updateItemTotal(row);
            //         calculateTotalPrice();
            //     });
            // } else {
                validateStock(row);
                validateAllStock();
                updateItemTotal(row);
                calculateTotalPrice();
            // }
        });

        $(document).on('change keyup', 'select[name*="[product_id]"], input[name*="[price]"]', function () {
            const row = $(this).closest('.item-row');
            if ($(this).is('select')) {
                updateHiddenFields($(this));
            }
            validateStock(row);
            validateAllStock();
            updateItemTotal(row);
            calculateTotalPrice();
        });

        function populateConfirmationModal() {
            const customer = $('#user_id option:selected').text() || 'Not selected';
            const shippingAddress = $('#shipping-address').val() || 'Not provided';
            const deliveryMethod = $('#delivery_method option:selected').text() || 'Not selected';
            const saveFuture = $('#safe_for_future').is(':checked') ? 'Yes' : 'No';
            const isPaid = $('#is_paid').is(':checked') ? 'Yes' : 'No';
            const quickOrder = $('#quick_order').is(':checked') ? 'Yes' : 'No';
            const subtotal = $('#subtotal').text();
            const shippingCost = $('#shipping-cost').text();
            const totalPrice = $('#total-price').text();
            const signatureData = $('#signature_data').val();

            $('#modal-customer').text(customer);
            $('#modal-shipping-address').text(shippingAddress);
            $('#modal-delivery-method').text(deliveryMethod);
            $('#modal-save-future').text(saveFuture);
            $('#modal-is-paid').text(isPaid);
            $('#modal-quick-order').text(quickOrder);
            $('#modal-subtotal').text(subtotal);
            $('#modal-shipping-cost').text(shippingCost);
            $('#modal-total-price').text(totalPrice);

            $('#modal-items').empty();
            $('#items .item-row').each(function() {
                const product = $(this).find('select[name*="[product_id]"] option:selected').text() || 'Not selected';
                const originalPrice = $(this).find('input[name*="[original_price]"]').val() || '0.00';
                const price = $(this).find('input[name*="[price]"]').val() || '0.00';
                const quantity = $(this).find('input[name*="[quantity]"]').val() || '0';
                const total = $(this).find('.item-total').text() || '0.00';
                $('#modal-items').append(`
                    <tr>
                        <td>${product}</td>
                        <td>$${originalPrice}</td>
                        <td>$${price}</td>
                        <td>${quantity}</td>
                        <td>$${total}</td>
                    </tr>
                `);
            });

            if (signatureData && $('#delivery_method').val() === 'instant') {
                $('#modal-signature').attr('src', signatureData);
                $('#modal-signature-section').show();
            } else {
                $('#modal-signature-section').hide();
            }
        }

        $('#edit-order').on('submit', function (e) {
            e.preventDefault();
            if ($('#delivery_method').val() === 'instant' && signaturePad.isEmpty()) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Customer signature is required for instant delivery.',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            const items = [];
            $('#items .item-row').each(function (index) {
                const productId = $(this).find('select[name*="[product_id]"]').val();
                const color = $(this).find('input[name*="[color]"]').val();
                const quantity = parseInt($(this).find('input[name*="[quantity]"]').val()) || 0;
                if (productId && color && quantity) {
                    items.push({
                        product_id: productId,
                        color: color,
                        quantity: quantity
                    });
                }
            });

            $.ajax({
                url: '{{ route("salesman.orders.check-stock") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    items: items
                },
                success: function (response) {
                    if (!signaturePad.isEmpty()) {
                        const dataUrl = signaturePad.toDataURL('image/png');
                        $('#signature_data').val(dataUrl);
                    }
                    populateConfirmationModal();
                    $('#orderConfirmationModal').modal('show');
                },
                error: function (xhr) {
                    if (xhr.status === 422 && xhr.responseJSON.errors) {
                        let errorMessage = 'The following items exceed available stock:\n';
                        xhr.responseJSON.errors.forEach(error => {
                            errorMessage += `- ${error.product}: Requested ${error.requested}, Available ${error.available}\n`;
                        });
                        Swal.fire({
                            title: 'Stock Error!',
                            text: errorMessage,
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Failed to validate stock availability.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                }
            });
        });

        $('#confirm-order-btn').on('click', function() {
            $('#orderConfirmationModal').modal('hide');
            $('#edit-order').off('submit').submit();
        });

        $('#quick_order').change(function(){
            if($(this).is(':checked')) {
                $('#quick_order_panel').show();
                const customerId = $('#user_id').val();
                if (customerId) {
                    loadPreviousOrders(customerId);
                }
            } else {
                $('#quick_order_panel').hide();
                previousOrdersTable.clear().draw();
            }
        });

        $('#addSelectedOrders').click(function(){
            $('#items .item-row').each(function() {
                const productId = $(this).find('select[name*="[product_id]"]').val();
                if (!productId) {
                    $(this).remove();
                }
            });

            if ($('#items .item-row').length === 0) {
                let row = $('#items .item-row').first().clone();
                row.find('select, input').each(function() {
                    const oldName = $(this).attr('name');
                    if (oldName) {
                        const newName = oldName.replace(/\[\d+\]/, '[0]');
                        $(this).attr('name', newName).val(
                            oldName.includes('quantity') ? '1' :
                            oldName.includes('price') || oldName.includes('original_price') ? '' :
                            oldName.includes('is_one_time') ? '1' : ''
                        );
                    }
                });
                row.find('.use-custom-price').prop('checked', false);
                row.find('.item-total').text('0.00');
                let selectElement = row.find('select[name^="items"]');
                selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
                selectElement.val('');
                selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
                row.find('.stock-feedback').remove();
                row.find('img').attr('src', '');
                $('#items').append(row);
                initSelect2(selectElement);
                itemIndex = 1;
            }

            var selectedOrders = [];
            $('#previousOrdersTable input[type="checkbox"]:checked').each(function(){
                var items = $(this).data('items');
                selectedOrders.push({
                    items: items
                });
            });

            if(selectedOrders.length === 0) {
                Swal.fire({
                    title: 'Error!',
                    text: 'Please select at least one previous order',
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                return;
            }

            selectedOrders.forEach(function(order) {
                order.items.forEach(function(item) {
                    let row = $('#items .item-row').first().clone();
                    row.find('select, input').each(function() {
                        const oldName = $(this).attr('name');
                        if (oldName) {
                            const newName = oldName.replace(/\[\d+\]/, `[${itemIndex}]`);
                            $(this).attr('name', newName);
                            if (oldName.includes('quantity')) {
                                $(this).val(item.quantity);
                            } else if (oldName.includes('price')) {
                                $(this).val(parseFloat(item.price).toFixed(2));
                            } else if (oldName.includes('original_price')) {
                                $(this).val('');
                            } else if (oldName.includes('color')) {
                                $(this).val(item.color);
                            } else if (oldName.includes('is_one_time')) {
                                $(this).val('1');
                            }
                        }
                    });
                    row.find('.use-custom-price').prop('checked', false);
                    row.find('.item-total').text(item.total);
                    let selectElement = row.find('select[name^="items"]');
                    selectElement.html('<option value="">-- Select Product --</option>' + productOptions.join(''));
                    selectElement.removeClass('select2-hidden-accessible').removeAttr('data-select2-id').next('.select2-container').remove();
                    row.find('.stock-feedback').remove();
                    row.find('img').attr('src', '');

                    $('#items').append(row);
                    initSelect2(selectElement);
                    selectElement.val(item.product_id).trigger('change.select2');
                    updateHiddenFields(selectElement, null, item.price);
                    validateStock(row);
                    validateAllStock();
                    itemIndex++;
                });
                calculateTotalPrice();
            });
        });

        calculateTotalPrice();
        const initialAddress = $('#shipping-address').val();
        const initialState = $('#user_id').find('option:selected').data('state') || '';
        calculateShipping(initialAddress, initialState);
    });
</script>
@endpush
