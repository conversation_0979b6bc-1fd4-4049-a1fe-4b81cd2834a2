@extends('backend.layouts.master')

@section('main-content')
<div class="card shadow mb-4">
    <div class="row">
        <div class="col-md-12">
            @include('backend.layouts.notification')
        </div>
    </div>
    <div class="card-header py-3">
        <h6 class="m-0 font-weight-bold text-primary float-left">Visit Reminders</h6>
        <a href="{{ route('salesman.visit.create') }}" class="btn btn-primary btn-sm float-right" data-toggle="tooltip" data-placement="bottom" title="Add Reminder"><i class="fas fa-plus"></i> Add Reminder</a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            @if($reminders->isEmpty())
                <p class="text-gray-500 text-center">No visit reminders found.</p>
            @else
                <table class="table table-bordered" id="reminder-dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>S.N.</th>
                            <th>Customer</th>
                            <th>Visit Date</th>
                            <th>Note</th>
                            <th>Recurring</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th>S.N.</th>
                            <th>Customer</th>
                            <th>Visit Date</th>
                            <th>Note</th>
                            <th>Recurring</th>
                            <th>Action</th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @foreach($reminders as $key => $reminder)
                            <tr>
                                <td>{{ $key + 1 }}</td>
                                <td>{{ $reminder->customer->first_name }} {{ $reminder->customer->last_name }}</td>
                                <td>{{ $reminder->visit_date }}</td>
                                <td>{{ $reminder->note ?? '-' }}</td>
                                <td>{{ $reminder->is_recurring ? 'Yes' : 'No' }}</td>
                                <td>
                                    <a href="{{ route('salesman.visit.edit', $reminder->id) }}" class="btn btn-primary btn-sm float-left mr-1" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" title="edit" data-placement="bottom"><i class="fas fa-edit"></i></a>
                                    <form method="POST" action="{{ route('salesman.visit.destroy', $reminder->id) }}">
                                        @csrf
                                        @method('delete')
                                        <button class="btn btn-danger btn-sm dltBtn" data-id="{{ $reminder->id }}" style="height:30px; width:30px;border-radius:50%" data-toggle="tooltip" data-placement="bottom" title="Delete"><i class="fas fa-trash-alt"></i></button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
    <link href="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert2.min.css" />
    <style>
        div.dataTables_wrapper div.dataTables_paginate {
            display: none;
        }
    </style>
@endpush

@push('scripts')
    <script src="{{ asset('backend/vendor/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/2.1.2/sweetalert2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#reminder-dataTable').DataTable({
                columnDefs: [
                    { orderable: false, targets: [5] }
                ]
            });

            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            $('.dltBtn').click(function(e) {
                e.preventDefault();
                var form = $(this).closest('form');
                var dataID = $(this).data('id');

                Swal.fire({
                    title: "Are you sure?",
                    text: "Once deleted, you will not be able to recover this reminder!",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Delete",
                    cancelButtonText: "Cancel",
                    dangerMode: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        form.submit();
                    }
                });
            });
        });
    </script>
@endpush
