const mix = require("laravel-mix");
const glob = require("glob");
const fs = require("fs");

function minifyCSSFiles() {
    glob.sync("public/**/*.css").forEach(file => {
        if (!file.endsWith(".min.css")) {
            let minifiedFile = file.replace(".css", ".min.css");

            if (fs.existsSync(minifiedFile)) {
                let originalTime = fs.statSync(file).mtimeMs;
                let minifiedTime = fs.statSync(minifiedFile).mtimeMs;
                if (originalTime > minifiedTime) {
                    console.log(`Minifying updated CSS: ${file}`);
                    mix.minify(file);
                }
            }
        }
    });
}

function minifyJSFiles() {
    glob.sync("public/**/*.js").forEach(file => {
        if (!file.endsWith(".min.js")) {
            let minifiedFile = file.replace(".js", ".min.js");

            if (fs.existsSync(minifiedFile)) {
                let originalTime = fs.statSync(file).mtimeMs;
                let minifiedTime = fs.statSync(minifiedFile).mtimeMs;
                if (originalTime > minifiedTime) {
                    console.log(`Minifying updated JS: ${file}`);
                    mix.minify(file);
                }
            }
        }
    });
}

function watchPublicFolder() {
    fs.watch("public", { recursive: true }, (eventType, filename) => {
        if (filename) {
            console.log(`File changed: ${filename}`);

            if (filename.endsWith(".css") && !filename.endsWith(".min.css")) {
                minifyCSSFiles();
            } else if (
                filename.endsWith(".js") &&
                !filename.endsWith(".min.js")
            ) {
                minifyJSFiles();
            }
        }
    });
}

mix.js("resources/js/app.js", "public/js").sass(
    "resources/sass/app.scss",
    "public/css"
);

watchPublicFolder();
minifyCSSFiles();
minifyJSFiles();

// mix.version();
