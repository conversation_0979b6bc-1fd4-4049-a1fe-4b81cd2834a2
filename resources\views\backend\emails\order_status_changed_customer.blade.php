@extends('emails.layouts.master')

@section('title', 'Order Status Update - Lamart Manufacturing')

@section('preheader', 'Your order #' . $order['order_number'] . ' status has been updated')

@section('content')
<!-- Status Update Message -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 2em 0 0 0;">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text">
                  <h2 style="color: #4D734E;">Order Status Update</h2>
                  <h4>
                     Hello {{$order['first_name']}} {{$order['last_name']}},<br><br>
                     We wanted to update you on the status of your recent order.
                  </h4>
                  <p>
                     Order Number: <strong>{{$order['order_number']}}</strong><br>
                     Order Date: {{date('M d, Y', strtotime($order['created_at']))}} <br>
                     Status Changed: From <strong>{{ucfirst($old_status)}}</strong> to <strong style="color: #4D734E;">{{ucfirst($new_status)}}</strong>
                  </p>
                  <p style="margin-top: 20px;">
                     <a href="https://lamartmfg.com/order/track?track=<?php echo base64_encode($order['order_number']); ?>" class="btn btn-primary" style="background: #4D734E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                        Track Your Order
                     </a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Status Details -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">What This Means</h3>
                  
                  @switch($new_status)
                     @case('processing')
                        <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #856404;">Order is Being Processed</h4>
                           <p style="margin: 0; color: #856404;">Your order is currently being prepared by our team. We're gathering all your items and getting them ready for shipment.</p>
                        </div>
                        @break
                     
                     @case('shipped')
                        <div style="background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #155724;">Order Has Shipped!</h4>
                           <p style="margin: 0; color: #155724;">Great news! Your order is on its way. You should receive it within the estimated delivery timeframe.</p>
                           @if(isset($order['tracking_number']))
                           <p style="margin: 10px 0 0 0; color: #155724;"><strong>Tracking Number:</strong> {{$order['tracking_number']}}</p>
                           @endif
                        </div>
                        @break
                     
                     @case('delivered')
                        <div style="background: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #0c5460;">Order Delivered</h4>
                           <p style="margin: 0; color: #0c5460;">Your order has been successfully delivered! We hope you're satisfied with your purchase.</p>
                        </div>
                        @break
                     
                     @case('cancelled')
                        <div style="background: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #721c24;">Order Cancelled</h4>
                           <p style="margin: 0; color: #721c24;">Your order has been cancelled. If you have any questions about this cancellation, please contact our customer service team.</p>
                        </div>
                        @break
                     
                     @case('on_hold')
                        <div style="background: #ffeaa7; border-left: 4px solid #fdcb6e; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #8b6914;">Order On Hold</h4>
                           <p style="margin: 0; color: #8b6914;">Your order is temporarily on hold. Our team will contact you shortly with more information.</p>
                        </div>
                        @break
                     
                     @default
                        <div style="background: #e2e3e5; border-left: 4px solid #6c757d; padding: 15px; margin: 15px 0;">
                           <h4 style="margin: 0 0 10px 0; color: #383d41;">Status: {{ucfirst($new_status)}}</h4>
                           <p style="margin: 0; color: #383d41;">Your order status has been updated. For more details, please track your order or contact our customer service.</p>
                        </div>
                  @endswitch
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Order Summary -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Order Summary</h3>
                  
                  <div style="background: #f9f9f9; padding: 20px; border-radius: 4px;">
                     <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                           <td style="padding: 5px 0;">Order Total:</td>
                           <td style="text-align: right; padding: 5px 0; font-weight: 600; color: #4D734E;">${{number_format($order['total_amount'], 2)}}</td>
                        </tr>
                        <tr>
                           <td style="padding: 5px 0;">Items:</td>
                           <td style="text-align: right; padding: 5px 0;">
                              @if(isset($order['cart_info']) && is_array($order['cart_info']))
                                 {{count($order['cart_info'])}} item(s)
                              @endif
                           </td>
                        </tr>
                        <tr>
                           <td style="padding: 5px 0;">Shipping Address:</td>
                           <td style="text-align: right; padding: 5px 0;">{{$order['address1']}}, {{$order['post_code']}}</td>
                        </tr>
                     </table>
                  </div>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Contact Information -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Need Help?</h3>
                  <p>If you have any questions about your order or need assistance, please don't hesitate to contact us:</p>
                  
                  <p style="margin-top: 20px;">
                     <a href="mailto:<EMAIL>" class="btn btn-primary" style="background: #4D734E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin-right: 10px;">
                        Contact Support
                     </a>
                     <a href="tel:(*************" class="btn" style="background: #f0f0f0; color: #333; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                        Call Us
                     </a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>
@endsection
