@extends('backend.layouts.master')

@section('title','Admin Profile')

@section('main-content')

<div class="container my-4">
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">📍 Filter Users by ZIP Codes</h5>
        </div>
        <div class="card-body">
            <form id="zipFilterForm" onsubmit="event.preventDefault(); filterUsers();">
                <div class="form-row">
                    <div class="form-group col-md-3">
                        <label for="zip1">ZIP Code 1</label>
                        <input type="text" id="zip1" class="form-control zip-input" placeholder="Enter ZIP Code">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="zip2">ZIP Code 2</label>
                        <input type="text" id="zip2" class="form-control zip-input" placeholder="Enter ZIP Code">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="zip3">ZIP Code 3</label>
                        <input type="text" id="zip3" class="form-control zip-input" placeholder="Enter ZIP Code">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="zip4">ZIP Code 4</label>
                        <input type="text" id="zip4" class="form-control zip-input" placeholder="Enter ZIP Code">
                    </div>
                </div>
                <button type="submit" class="btn btn-success mt-2">
                    🔍 Filter Users
                </button>
                <p class="mt-3 mb-0"><strong>Users found:</strong> <span id="user-count">0</span></p>
            </form>
        </div>
    </div>
</div>
<div style="height: calc(100vh - 70px); width: 100%;">
        <div id="map" style="height: 100%; width: 100%;"></div>
    </div>
@endsection
<link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css" />
<style>
    .breadcrumbs{
        list-style: none;
    }
    .breadcrumbs li{
        float:left;
        margin-right:10px;
    }
    .breadcrumbs li a:hover{
        text-decoration: none;
    }
    .breadcrumbs li .active{
        color:red;
    }
    .breadcrumbs li+li:before{
      content:"/\00a0";
    }
    .image{
        background:url('{{asset('backend/img/background.jpg')}}');
        height:150px;
        background-position:center;
        background-attachment:cover;
        position: relative;
    }
    .image img{
        position: absolute;
        top:55%;
        left:35%;
        margin-top:30%;
    }
    i{
        font-size: 14px;
        padding-right:8px;
    }
  </style> 

@push('scripts')
<script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
<script>
    const allUsers = @json($users);
    const map = L.map('map');

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    let markers = [];

    const colorMap = {}; // ZIP -> color
    const colors = ['red', 'blue', 'green', 'orange'];

    function renderUsers(users) {
        // Clear existing markers
        markers.forEach(m => map.removeLayer(m));
        markers = [];
        const bounds = [];

        users.forEach(user => {
            const lat = parseFloat(user.shipping_lat);
            const lng = parseFloat(user.shipping_long);

            if (!isNaN(lat) && !isNaN(lng)) {
                const position = [lat, lng];
                bounds.push(position);

                const marker = L.marker(position)
                    .addTo(map)
                    .bindPopup(`<strong>${user.first_name} ${user.last_name}</strong><br>${user.email}<br>ZIP: ${user.shipping_zip}`);
                markers.push(marker);
            }
        });

        if (bounds.length > 0) {
            map.fitBounds(bounds);
        } else {
            map.setView([20.5937, 78.9629], 5); // fallback
        }

        document.getElementById('user-count').innerHTML = `Users found: ${users.length}`;
    }

    function filterUsers() {
        const zips = Array.from(document.querySelectorAll('.zip-input'))
            .map(input => input.value.trim())
            .filter(zip => zip.length > 0);
        console.log(zips);

        if (zips.length === 0) {
            return renderUsers(allUsers);
        }

        const filtered = allUsers.filter(user => 
            zips.includes((user.shipping_zip || '').trim())
        );
        renderUsers(filtered);
    }

    // Initial load
    renderUsers(allUsers);
</script>
@endpush