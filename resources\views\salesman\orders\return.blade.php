@extends('backend.layouts.master')

@section('title', 'Order Return')

@section('main-content')
<div class="container-fluid py-4">
    <div class="card shadow-sm border-0">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Order Return Request</h5>
            <a href="{{ route('salesman.orders.edit', $order->id) }}" class="btn btn-sm btn-light shadow-sm">
                <i class="fas fa-arrow-left fa-sm"></i> Back to Order
            </a>
        </div>
        <div class="card-body p-4">
            @if($order)
                <div class="row mb-4">
                    <div class="col-12">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>S.N.</th>
                                    <th>Order No.</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Quantity</th>
                                    <th>Shipping Charge</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $order->id }}</td>
                                    <td>{{ $order->order_number }}</td>
                                    <td>{{ $order->first_name }} {{ $order->last_name }}</td>
                                    <td>{{ $order->email }}</td>
                                    <td>{{ $order->quantity }}</td>
                                    <td>${{ number_format($order->shipping_price ?? 0, 2) }}</td>
                                    <td>${{ number_format($order->total_amount, 2) }}</td>
                                    <td>
                                        @if($order->status == 'new')
                                            <span class="badge badge-primary">{{ $order->status }}</span>
                                        @elseif($order->status == 'process')
                                            <span class="badge badge-warning">{{ $order->status }}</span>
                                        @elseif($order->status == 'delivered')
                                            <span class="badge badge-success">{{ $order->status }}</span>
                                        @else
                                            <span class="badge badge-danger">{{ $order->status }}</span>
                                        @endif
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-lg-6 mb-4">
                        <div class="order-info p-4 rounded">
                            <h4 class="text-center pb-3">Order Information</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Number</strong></td>
                                    <td>: {{ $order->order_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Date</strong></td>
                                    <td>: {{ $order->created_at->format('D d M, Y') }} at {{ $order->created_at->format('g:i a') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Quantity</strong></td>
                                    <td>: {{ $order->quantity }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Status</strong></td>
                                    <td>: {{ $order->status }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Shipping Charge</strong></td>
                                    <td>: ${{ number_format($order->shipping_price ?? 0, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Coupon</strong></td>
                                    <td>: ${{ number_format($order->coupon ?? 0, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Amount</strong></td>
                                    <td>: ${{ number_format($order->total_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method</strong></td>
                                    <td>: {{ ucwords(str_replace('_', ' ', $order->payment_method)) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Status</strong></td>
                                    <td>: {{ $order->payment_status }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <div class="col-lg-6 mb-4">
                        <div class="shipping-info p-4 rounded">
                            <h4 class="text-center pb-3">Shipping Information</h4>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Full Name</strong></td>
                                    <td>: {{ $order->first_name }} {{ $order->last_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email</strong></td>
                                    <td>: {{ $order->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone No.</strong></td>
                                    <td>: {{ $order->phone }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Address</strong></td>
                                    <td>: {{ $order->address1 }}{{ $order->address2 ? ', ' . $order->address2 : '' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Country</strong></td>
                                    <td>: {{ $order->country }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Post Code</strong></td>
                                    <td>: {{ $order->post_code }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>

                <form method="POST" action="{{ route('salesman.return.store') }}" id="return-form">
                    @csrf
                    <input type="hidden" name="order_id" value="{{ $order->id }}">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card p-4">
                                <h5 class="fw-semibold mb-3">Return Type</h5>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="return_type" id="full_return" value="full" required>
                                    <label class="form-check-label" for="full_return">Full Order Return</label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="radio" name="return_type" id="partial_return" value="partial" required>
                                    <label class="form-check-label" for="partial_return">Partial Order Return</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4" id="partial-return-section" style="display: none;">
                        <div class="col-12">
                            <div class="card p-4">
                                <h5 class="fw-semibold mb-3">Select Items to Return</h5>
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Select</th>
                                            <th>Product</th>
                                            <th>Price</th>
                                            <th>Quantity</th>
                                            <th>Return Quantity</th>
                                            <th>Total</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($order->items as $index => $item)
                                            <tr>
                                                <td>
                                                    <input type="checkbox" name="items[{{ $index }}][selected]" class="return-item-checkbox" data-index="{{ $index }}">
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        {{ $item->product->title }}
                                                    </div>
                                                    <input type="hidden" name="items[{{ $index }}][product_id]" value="{{ $item->product_id }}">
                                                    <input type="hidden" name="items[{{ $index }}][color_id]" value="{{ $item->color }}">
                                                </td>
                                                <td>${{ number_format($item->price, 2) }}</td>
                                                <td>{{ $item->quantity }}</td>
                                                <td>
                                                    <input type="number" name="items[{{ $index }}][return_quantity]" class="form-control return-quantity" min="0" max="{{ $item->quantity }}" value="0" disabled>
                                                </td>
                                                <td>${{ number_format($item->price * $item->quantity, 2) }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card p-4">
                                <h5 class="fw-semibold mb-3">Return Details</h5>
                                <div class="form-group">
                                    <label for="return_reason" class="form-label fw-semibold">Reason for Return <span class="text-danger">*</span></label>
                                    <textarea name="return_reason" id="return_reason" class="form-control" rows="4" required></textarea>
                                    @error('return_reason')
                                        <div class="invalid-feedback d-block">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3">
                        <a href="{{ route('salesman.orders.edit', $order->id) }}" class="btn btn-outline-secondary">Cancel</a>
                        <button type="submit" id="submit-return-btn" class="btn btn-success" disabled>Submit Return Request</button>
                    </div>
                </form>
            @else
                <div class="alert alert-warning">Order not found.</div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .order-info, .shipping-info {
        background: #ECECEC;
        padding: 20px;
        border-radius: 8px;
    }
    .order-info h4, .shipping-info h4 {
        text-decoration: underline;
        margin-bottom: 1rem;
    }
    .table th, .table td {
        vertical-align: middle;
    }
</style>
@endpush

@push('scripts')
<script>
    $(document).ready(function() {
        // Toggle partial return section based on return type
        $('input[name="return_type"]').on('change', function() {
            const isPartial = $(this).val() === 'partial';
            $('#partial-return-section').toggle(isPartial);
            $('.return-item-checkbox').prop('checked', false);
            $('.return-quantity').prop('disabled', true).val(0);
            $('#submit-return-btn').prop('disabled', !$(this).val());
        });

        // Enable/disable return quantity input based on checkbox
        $('.return-item-checkbox').on('change', function() {
            const index = $(this).data('index');
            const returnQuantityInput = $(`input[name="items[${index}][return_quantity]"]`);
            returnQuantityInput.prop('disabled', !$(this).is(':checked'));
            if (!$(this).is(':checked')) {
                returnQuantityInput.val(0);
            }

            // Enable/disable submit button based on selected items for partial return
            const isPartial = $('input[name="return_type"]:checked').val() === 'partial';
            const anySelected = $('.return-item-checkbox:checked').length > 0;
            $('#submit-return-btn').prop('disabled', isPartial && !anySelected);
        });

        // Validate return quantity
        $('.return-quantity').on('input', function() {
            const max = parseInt($(this).attr('max'));
            const value = parseInt($(this).val());
            if (value > max) {
                $(this).val(max);
            } else if (value < 0 || isNaN(value)) {
                $(this).val(0);
            }
        });

        // Enable submit button when full return is selected
        $('#full_return').on('change', function() {
            if ($(this).is(':checked')) {
                $('#submit-return-btn').prop('disabled', false);
            }
        });
    });
</script>
@endpush
