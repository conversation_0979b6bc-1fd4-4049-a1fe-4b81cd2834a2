@extends('backend.layouts.master')

@section('title','Create Review')

@section('main-content')
<div class="card">
  <h5 class="card-header">Create Review</h5>
  <div class="card-body">
    <form action="{{route('review.store')}}" method="POST" enctype="multipart/form-data">
      @csrf

      <div class="form-group">
        <label for="user_id">User:</label>
        <select name="user_id" class="form-control" required>
          <option value="">-- Select User --</option>
          @foreach($users as $user)
            <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
              {{ $user->first_name }} {{ $user->last_name }} ({{ $user->email }})
            </option>
          @endforeach
        </select>
        @error('user_id')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="order_id">Order:</label>
        <select name="order_id" class="form-control" required>
          <option value="">-- Select Order --</option>
          @foreach($orders as $order)
            <option value="{{ $order->id }}" {{ old('order_id') == $order->id ? 'selected' : '' }}>
              Order #{{ $order->order_number }} - {{ $order->first_name }} {{ $order->last_name }}
            </option>
          @endforeach
        </select>
        @error('order_id')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="product_id">Product:</label>
        <select name="product_id" class="form-control" required>
          <option value="">-- Select Product --</option>
          @foreach($products as $product)
            <option value="{{ $product->id }}" {{ old('product_id') == $product->id ? 'selected' : '' }}>
              {{ $product->title }} ({{ $product->price }})
            </option>
          @endforeach
        </select>
        @error('product_id')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="title">Review Title:</label>
        <input type="text" name="title" class="form-control" value="{{ old('title') }}" required>
        @error('title')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="review">Review Content:</label>
        <textarea name="review" cols="20" rows="5" class="form-control" required>{{ old('review') }}</textarea>
        @error('review')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="rate">Rating:</label>
        <select name="rate" class="form-control" required>
          <option value="">-- Select Rating --</option>
          <option value="1" {{ old('rate') == 1 ? 'selected' : '' }}>1 Star</option>
          <option value="2" {{ old('rate') == 2 ? 'selected' : '' }}>2 Stars</option>
          <option value="3" {{ old('rate') == 3 ? 'selected' : '' }}>3 Stars</option>
          <option value="4" {{ old('rate') == 4 ? 'selected' : '' }}>4 Stars</option>
          <option value="5" {{ old('rate') == 5 ? 'selected' : '' }}>5 Stars</option>
        </select>
        @error('rate')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="images">Review Images:</label>
        <input type="file" name="images[]" class="form-control" multiple>
        <small class="text-muted">You can select multiple images</small>
        @error('images')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="status">Status:</label>
        <select name="status" class="form-control" required>
          <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>Active</option>
          <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
        </select>
        @error('status')
          <span class="text-danger">{{ $message }}</span>
        @enderror
      </div>

      <div class="form-group">
        <label for="moderator_notes">Moderator Notes:</label>
        <textarea name="moderator_notes" class="form-control" rows="3">{{ old('moderator_notes') }}</textarea>
        <small class="text-muted">Internal notes only, not visible to customers</small>
      </div>

      <div class="form-group">
        <button type="submit" class="btn btn-primary">Submit</button>
        <a href="{{ route('review.index') }}" class="btn btn-secondary">Cancel</a>
      </div>
    </form>
  </div>
</div>
@endsection

@push('styles')
<style>
    .img-thumbnail {
        object-fit: cover;
    }
    .form-group {
        margin-bottom: 1.5rem;
    }
</style>
@endpush
