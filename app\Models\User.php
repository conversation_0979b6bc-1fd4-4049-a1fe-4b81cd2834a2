<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    use HasFactory;
    use Notifiable;

    protected $table = 'users';

    protected $fillable = [
        'email', 'password','role','photo','status','provider','provider_id', 'first_name', 'last_name', 'email', 'password', 'password_confirmation', 'company_name', 'billing_address', 'billing_city', 'billing_state', 'billing_zip', 'same_as_billing', 'shipping_address', 'shipping_city', 'shipping_state', 'shipping_zip', 'contact_phone', 'account_phone', 'w9_name', 'w9_business_name', 'w9_tax_classification', 'w9_street_address', 'w9_city', 'w9_state', 'w9_zip_code', 'w9_tin', 'shipping_lat' , 'shipping_long' , 'billing_lat' , 'billing_long', 'salesman_id', 'pwd_updated', 'is_imported'
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    public function orders(){
        return $this->hasMany('App\Models\Order');
    }

    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'permission_user');
    }

    public function hasRole($role)
    {
        return $this->role === $role;
    }

    public function attachPermission($permission)
    {
        if (is_string($permission)) {
            $permission = Permission::where('slug', $permission)->firstOrFail();
        }

        $this->permissions()->syncWithoutDetaching($permission);
    }

    public function hasPermission($slug)
    {
        if ($this->permissions->contains('slug', $slug)) {
            return true;
        }

        if ($this->role && $this->role->permissions->contains('slug', $slug)) {
            return true;
        }

        return false;
    }

    public function emails()
    {
        return $this->hasMany(UserEmail::class);
    }

    public function priceLevelLists()
    {
        return $this->belongsToMany(UserPriceLevelList::class, 'item_price_level_lists')->withPivot('price');
    }

    public function categories()
    {
        return $this->belongsToMany(UserCategory::class, 'item_categories');
    }

    public function shippingPreference()
    {
        return $this->hasOne(ShippingPreference::class);
    }

    public function paymentPreference()
    {
        return $this->hasOne(PaymentPreference::class);
    }

    public function assignedCustomers()
    {
        return User::whereIn    ('role', ['customer','user'])->where('salesman_id', $this->id)->get();
    }

    public function additionalPhoneNumbers()
    {
        return $this->hasMany(AdditionalUsersPhoneNumbers::class);
    }
}
