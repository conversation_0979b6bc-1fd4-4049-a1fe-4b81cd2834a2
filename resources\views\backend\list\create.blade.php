@extends('backend.layouts.master')

@section('main-content')
<div class="card">
    <h5 class="card-header">Add Price Level List</h5>
    <div class="card-body">
        <form method="post" action="{{route('list.store')}}">
            {{csrf_field()}}

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="inputTitle" class="col-form-label">Price Level Name <span class="text-danger">*</span></label>
                        <input id="inputTitle" type="text" name="title" placeholder="Enter price level name" value="{{old('title')}}" class="form-control">
                        @error('title')
                        <span class="text-danger">{{$message}}</span>
                        @enderror
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="products">Select Products <span class="text-danger">*</span></label>
                <select name="products[]" id="products" class="form-control select2" multiple required>
                    <option value="new"><strong>Create New Product</strong></option>
                    @foreach($products as $product)
                        @if($product->item_colors && $product->item_colors->count() > 0)
                            @foreach($product->item_colors as $itemColor)
                                <option value="{{ $product->id . '-' . $itemColor->color->id }}"
                                        data-item-number="{{ $itemColor->item_number }}"
                                        data-item-name="{{ $product->title }}"
                                        data-price="{{ $itemColor->price }}"
                                        data-color="{{ $itemColor->color->id }}"
                                        {{ in_array($product->id . '-' . $itemColor->color->id, old('products', [])) ? 'selected' : '' }}>
                                    {{ $itemColor->item_number }} - {{ $product->title }} ({{ $itemColor->color->name }})
                                </option>
                            @endforeach
                        @endif
                    @endforeach
                </select>
                @error('products')
                <span class="text-danger">{{ $message }}</span>
                @enderror
            </div>

            <div class="form-group">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="inactive" id="inactive" value="1" {{old('inactive') ? 'checked' : ''}}>
                    <label class="form-check-label" for="inactive">
                        Price Level is inactive
                    </label>
                </div>
            </div>

            <div class="card mt-4" id="pricing-table-card" style="display: none;">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Product Pricing</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered table-sm" id="product-pricing-table">
                        <thead>
                            <tr>
                                <th width="30%">ITEM</th>
                                <th width="35%">STANDARD PRICE</th>
                                <th width="35%">CUSTOM PRICE</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>

            <div class="form-group mt-4">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button class="btn btn-success" type="submit">Submit</button>
                <a href="{{ route('list.index') }}" class="btn btn-secondary">Cancel</a>
            </div>
        </form>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('backend/js/select2.min.js') }}"></script>
<script>
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: 'Select products',
        allowClear: true
    });

    // Load table on page load if products are pre-selected (old values)
    if ($('#products').val() && $('#products').val().length > 0) {
        updatePricingTable();
    }

    // Update pricing table when products are selected/deselected
    $('#products').on('change', function() {
        const selectedProducts = $(this).val() || [];
        if(selectedProducts.includes('new')){
            // Remove 'new' from selection
            const filteredProducts = selectedProducts.filter(product => product !== 'new');
            $(this).val(filteredProducts).trigger('change');

            // Show create product modal or redirect
            if(confirm('Do you want to create a new product? You will be redirected to the product creation page.')){
                window.open('{{ route("product.create") }}', '_blank');
            }
            return;
        }
        updatePricingTable();
    });

    function updatePricingTable() {
        let selectedProducts = $('#products').val();
        let tbody = $('#product-pricing-table tbody');

        // Clear existing rows
        tbody.empty();

        if (!selectedProducts || selectedProducts.length === 0) {
            $('#pricing-table-card').hide();
            return;
        }

        $('#pricing-table-card').show();

        // Add rows for each selected product
        selectedProducts.forEach(function(productId) {
            let option = $('#products option[value="' + productId + '"]');

            let sku = option.data('item-name');
            let standardPrice = parseFloat(option.data('price'));

            let oldCustomPrice = getOldCustomPrice(productId);

            let row = `
                <tr>
                    <td>${sku}</td>
                    <td>${standardPrice.toFixed(2)}</td>
                    <td>
                        <input type="number"
                               name="custom_prices[${productId}]"
                               class="form-control form-control-sm custom-price"
                               step="0.01"
                               value="${oldCustomPrice || standardPrice.toFixed(2)}"
                               data-product-id="${productId}"
                               required>
                    </td>
                </tr>
            `;

            tbody.append(row);
        });
    }

    // Helper function to get old custom price value
    function getOldCustomPrice(productId) {
        // This would be populated from Laravel's old() helper if available
        @if(old('custom_prices'))
            let oldPrices = @json(old('custom_prices'));
            return oldPrices[productId] || null;
        @else
            return null;
        @endif
    }

    // Form validation
    $('form').on('submit', function(e) {
        let title = $('#inputTitle').val().trim();
        let products = $('#products').val();

        if (!title) {
            e.preventDefault();
            Swal.fire('Error', 'Price Level Name is required', 'error');
            return false;
        }

        if (!products || products.length === 0) {
            e.preventDefault();
            Swal.fire('Error', 'Please select at least one product', 'error');
            return false;
        }

        // Validate all custom prices are filled
        let invalidPrices = false;
        $('.custom-price').each(function() {
            if (!$(this).val() || parseFloat($(this).val()) < 0) {
                invalidPrices = true;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (invalidPrices) {
            e.preventDefault();
            Swal.fire('Error', 'Please enter valid custom prices for all selected products', 'error');
            return false;
        }
    });

    // Reset form handler
    $('button[type="reset"]').on('click', function() {
        $('.select2').val(null).trigger('change');
        $('#pricing-table-card').hide();
    });
});
</script>
@endpush
