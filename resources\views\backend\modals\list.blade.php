<!-- List Modal -->
<div class="modal fade" id="listModal" tabindex="-1" role="dialog" aria-labelledby="listModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <form id="listForm">
        @csrf
        <div class="modal-header">
          <h5 class="modal-title">Add New List</h5>
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        </div>
        <div class="modal-body">
          <div class="form-group">
            <label for="inputTitle">Title <span class="text-danger">*</span></label>
            <input type="text" name="title" class="form-control" placeholder="Enter title">
            <div class="text-danger error-title"></div>
          </div>

          <div class="form-group">
            <label for="price_type">Type</label>
            <select name="price_type" class="form-control">
              <option value="">--Select any type--</option>
              <option value="fixed">Fixed</option>
              <option value="percentage">Percentage</option>
            </select>
            <div class="text-danger error-price_type"></div>
          </div>

          <div class="form-group">
            <label>Amount <span class="text-danger">*</span></label>
            <input type="text" name="price_value" class="form-control" placeholder="Enter amount">
            <div class="text-danger error-price_value"></div>
          </div>

          <div class="form-group">
            <label for="method">Method</label>
            <select name="method" class="form-control">
              <option value="">--Select any method--</option>
              <option value="plus">Increment</option>
              <option value="minus">Decrement</option>
            </select>
            <div class="text-danger error-method"></div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label>Products</label>
                <select name="products[]" class="form-control select2" multiple required>
                  @foreach($products as $product)
                  <option value="{{ $product->id }}">{{ $product->title }}</option>
                  @endforeach
                </select>
                <div class="text-danger error-products"></div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label>Users <span class="text-danger">*</span></label>
                <select name="users[]" class="form-control select2" multiple required>
                  @foreach($users as $user)
                  <option value="{{ $user->id }}">{{ $user->first_name . ' ' . $user->last_name . ' --- (' . $user->email . ')' }}</option>
                  @endforeach
                </select>
                <div class="text-danger error-users"></div>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>Status <span class="text-danger">*</span></label>
            <select name="status" class="form-control">
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <div class="text-danger error-status"></div>
          </div>

          <div class="card mt-4">
            <h5 class="card-header">Selected Product Details</h5>
            <div class="card-body">
              <table class="table table-bordered" id="product-details-table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Color</th>
                    <th>Size</th>
                    <th>Base Price</th>
                    <th>Calculated Price</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="reset" class="btn btn-warning">Reset</button>
          <button type="submit" class="btn btn-success">Submit</button>
        </div>
      </form>
    </div>
  </div>
</div>
