<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PriceLevelList;
use App\Models\User;
use App\Models\Product;
use App\Models\UserPriceLevelList;
use App\Models\ItemPriceLevelList;
use Illuminate\Support\Facades\DB;

class PriceLevelListController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $lists = PriceLevelList::orderBy('id','DESC')->paginate(10);
        return view('backend.list.index', compact('lists'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $products = Product::all();
        $users = User::all();
        return view('backend.list.create', compact('products', 'users'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // dd($request->all());
        $validatedData = $request->validate([
            'title' => 'required|string|unique:price_level_lists,title',
            'inactive' => 'nullable|in:on,0,1',
            'products' => 'required|array|min:1',
            'products.*' => 'required|string',
            'custom_prices' => 'required|array',
            'custom_prices.*' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $list = PriceLevelList::create([
                'title' => $validatedData['title'],
                'status' => $request->boolean('inactive') ? 'inactive' : 'active',
            ]);

            if ($request->products) {
                foreach ($request->products as $productColorKey) {
                    $parts = explode('-', $productColorKey);
                    $productId = $parts[0];
                    $colorId = $parts[1] ?? null;

                    ItemPriceLevelList::create([
                        'price_level_list_id' => $list->id,
                        'product_id' => $productId,
                        'color_id' => $colorId,
                        'custom_price' => $request->custom_prices[$productColorKey] ?? 0,
                    ]);
                }
            }

            $message = $list
                ? 'Price level list successfully added'
                : 'Error occurred, Please try again!';

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $message = 'Error occurred, Please try again!';
            $list = false;
        }

        if ($request->ajax()) {
            return response()->json([
                'id' => $list->id,
                'name' => $list->title
            ]);
        }

        return redirect()->route('list.index')->with(
            $list ? 'success' : 'error',
            $message
        );
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Implement if needed
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $list = PriceLevelList::findOrFail($id);
        $products = Product::all();
        $users = User::all();
        $user_price_level_lists = UserPriceLevelList::where('price_level_list_id', $id)->get();
        $item_price_level_lists = ItemPriceLevelList::where('price_level_list_id', $id)->get();
        return view('backend.list.edit', compact('list', 'products', 'users', 'user_price_level_lists', 'item_price_level_lists'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->all());
        $list = PriceLevelList::findOrFail($id);

        $validatedData = $request->validate([
            'title' => 'required|string|unique:price_level_lists,title,'.$id,
            'inactive' => 'boolean',
            'products' => 'required|array|min:1',
            'products.*' => 'required|string',
            'custom_prices' => 'required|array',
            'custom_prices.*' => 'required|numeric|min:0',
        ]);

        try {
            DB::beginTransaction();

            $status = $list->update([
                'title' => $validatedData['title'],
                'status' => $request->boolean('inactive') ? 'inactive' : 'active',
            ]);

            if ($request->products) {
                ItemPriceLevelList::where('price_level_list_id', $list->id)->delete();

                foreach ($request->products as $productColorKey) {
                    $parts = explode('-', $productColorKey);
                    $productId = $parts[0];
                    $colorId = $parts[1] ?? null;

                    ItemPriceLevelList::create([
                        'price_level_list_id' => $list->id,
                        'product_id' => $productId,
                        'color_id' => $colorId,
                        'custom_price' => $request->custom_prices[$productColorKey] ?? 0,
                    ]);
                }
            }

            $message = $status
                ? 'Price level list successfully updated'
                : 'Error occurred, Please try again!';

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $message = 'Error occurred, Please try again!';
            $status = false;
        }

        return redirect()->route('list.index')->with(
            $status ? 'success' : 'error',
            $message
        );
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $list = PriceLevelList::findOrFail($id);
        $status = $list->delete();

        $message = $status
            ? 'List successfully deleted'
            : 'Error while deleting list';

        return redirect()->route('list.index')->with(
            $status ? 'success' : 'error',
            $message
        );
    }

    public function getProductDetails(Request $request)
    {
        $productIds = $request->input('product_ids', []);
        $priceType = $request->input('price_type');
        $priceValue = $request->input('price_value');
        $method = $request->input('method');

        $products = Product::whereIn('id', $productIds)
            ->with(['item_colors' => function ($query) {
                $query->with(['color' => function ($query) {
                    $query->select('id', 'name');
                }])->select('id', 'product_id', 'color_id', 'price as base_price');
            }])
            ->get(['id', 'title as name', 'size']);

        $products = $products->map(function ($product) use ($priceType, $priceValue, $method) {
            $product->variants = $product->item_colors->map(function ($itemColor) use ($priceType, $priceValue, $method, $product) {
                $variant = new \stdClass();
                $variant->color = $itemColor->color->name;
                $variant->size = $product->size;
                $variant->base_price = $itemColor->base_price;

                $calculatedPrice = $itemColor->base_price;

                if ($priceType === 'fixed') {
                    $calculatedPrice = $method === 'plus'
                        ? $itemColor->base_price + $priceValue
                        : $itemColor->base_price - $priceValue;
                } elseif ($priceType === 'percentage') {
                    $factor = $method === 'plus'
                        ? 1 + ($priceValue / 100)
                        : 1 - ($priceValue / 100);
                    $calculatedPrice = $itemColor->base_price * $factor;
                }

                $variant->calculated_price = number_format($calculatedPrice, 2);
                return $variant;
            });
            unset($product->item_colors);
            return $product;
        });

        return response()->json(['products' => $products]);
    }
}
