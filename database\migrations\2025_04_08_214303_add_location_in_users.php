<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('billing_lat')->after('billing_address');
            $table->string('billing_long')->after('billing_lat');
            $table->string('shipping_lat')->after('shipping_address');
            $table->string('shipping_long')->after('shipping_address');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('billing_lat');
            $table->dropColumn('billing_long');
            $table->dropColumn('shipping_lat');
            $table->dropColumn('shipping_long');
        });
    }
};
