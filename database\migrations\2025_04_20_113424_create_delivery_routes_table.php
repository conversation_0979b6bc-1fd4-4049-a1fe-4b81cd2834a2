<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_routes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('salesman_id');
            $table->date('route_date');
            $table->string('route_name');
            $table->enum('status', ['planned', 'in_progress', 'completed', 'canceled'])->default('planned');
            $table->decimal('start_lat', 10, 8)->nullable();
            $table->decimal('start_lng', 11, 8)->nullable();
            $table->decimal('end_lat', 10, 8)->nullable();
            $table->decimal('end_lng', 11, 8)->nullable();
            $table->integer('estimated_duration')->nullable()->comment('In minutes');
            $table->integer('estimated_distance')->nullable()->comment('In meters');
            $table->json('optimized_waypoints')->nullable()->comment('Google Maps optimized order');
            $table->json('original_waypoints')->nullable()->comment('Original order before optimization');
            $table->timestamps();

            $table->foreign('salesman_id')->references('id')->on('users')->onDelete('cascade');
        });

        DB::statement('CREATE INDEX idx_delivery_routes_salesman_date ON delivery_routes (salesman_id, route_date)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_routes');
    }
};
