<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipping_preferences', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->enum('preferred_method', ['ship', 'delivery', 'pickup']);
            $table->string('carrier_preference')->nullable();
            $table->time('delivery_window_start')->nullable();
            $table->time('delivery_window_end')->nullable();
            $table->boolean('is_default')->default(false);
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });

        DB::statement('CREATE INDEX idx_shipping_prefs_user_default ON shipping_preferences (user_id, is_default)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipping_preferences');
    }
};
