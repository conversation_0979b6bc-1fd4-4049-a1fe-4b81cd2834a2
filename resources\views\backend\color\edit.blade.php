@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Edit Color</h5>
    <div class="card-body">
      <form method="post" action="{{route('color.update',$color->id)}}">
        @csrf
        @method('PATCH')
        <div class="form-group">
            <label for="name" class="col-form-label">Name <span class="text-danger">*</span></label>
            <input id="name" type="text" name="name" placeholder="Enter name"  value="{{old('name', $color->name)}}" class="form-control">
            @error('name')
                <span class="text-danger">{{$message}}</span>
            @enderror
        </div>


        <div class="form-group">
                <label for="color" class="col-form-label">Color <span class="text-danger">*</span></label>
                <input id="color" type="color" name="color" placeholder="Enter color"  value="{{old('color', $color->color)}}" class="form-control color-input">
                @error('color')
                    <span class="text-danger">{{$message}}</span>
                @enderror
            </div>

        <div class="form-group">
            <label for="status" class="col-form-label">Status <span class="text-danger">*</span></label>
            <select name="status" class="form-control">
                <option value="active" {{(($color->status=='active')? 'selected' : '')}}>Active</option>
                <option value="inactive" {{(($color->status=='active')? 'selected' : '')}}>Inactive</option>
            </select>
            @error('status')
                <span class="text-danger">{{$message}}</span>
            @enderror
        </div>
        <div class="form-group mb-3">
           <button class="btn btn-success" type="submit">Update</button>
        </div>
      </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="{{asset('backend/summernote/summernote.min.css')}}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.1/css/bootstrap-select.css" />

@endpush
@push('scripts')
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{asset('backend/summernote/summernote.min.js')}}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.13.1/js/bootstrap-select.min.js"></script>

<script>
    $('#lfm').filemanager('image');
</script>
@endpush
