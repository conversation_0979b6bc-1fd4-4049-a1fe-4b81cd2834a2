<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            ALTER TABLE orders
            DROP COLUMN payment_status,
            DROP COLUMN status,
            DROP COLUMN payment_method
        ");

        DB::statement("
            ALTER TABLE orders
            ADD COLUMN salesman_id BIGINT UNSIGNED NULL AFTER user_id,
            ADD COLUMN is_draft BOOLEAN DEFAULT FALSE AFTER user_id,
            ADD COLUMN is_quick_order BOOLEAN DEFAULT FALSE AFTER is_draft,
            ADD COLUMN is_instant_order BOOLEAN DEFAULT FALSE AFTER is_quick_order,
            ADD COLUMN original_order_id BIGINT UNSIGNED NULL AFTER is_instant_order,
            ADD COLUMN signature_data TEXT NULL AFTER original_order_id,
            ADD COLUMN interaction_log_id BIGINT UNSIGNED NULL AFTER signature_data,
            ADD COLUMN shipping_preference_id BIGINT UNSIGNED NULL AFTER interaction_log_id,
            ADD COLUMN return_status ENUM('none', 'requested', 'approved', 'rejected', 'processed') DEFAULT 'none' AFTER shipping_preference_id,
            ADD COLUMN credit_memo_id VARCHAR(255) NULL AFTER return_status,
            ADD COLUMN delivery_instructions TEXT NULL AFTER credit_memo_id,
            ADD COLUMN payment_status ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded') NOT NULL DEFAULT 'pending',
            ADD COLUMN status ENUM('draft', 'pending', 'processing', 'shipped', 'delivered', 'cancelled', 'returned') NOT NULL DEFAULT 'pending',
            ADD COLUMN payment_method ENUM('credit_card', 'bank_transfer', 'cash_on_delivery', 'check') NOT NULL DEFAULT 'credit_card'
        ");

        DB::statement("
            ALTER TABLE orders
            ADD CONSTRAINT fk_orders_salesman
            FOREIGN KEY (salesman_id) REFERENCES users(id) ON DELETE SET NULL
        ");

        DB::statement("
            ALTER TABLE orders
            ADD CONSTRAINT fk_orders_original
            FOREIGN KEY (original_order_id) REFERENCES orders(id) ON DELETE SET NULL
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("
            ALTER TABLE orders
            DROP FOREIGN KEY fk_orders_salesman,
            DROP FOREIGN KEY fk_orders_original
        ");

        DB::statement("
            ALTER TABLE orders
            DROP COLUMN salesman_id,
            DROP COLUMN is_draft,
            DROP COLUMN is_quick_order,
            DROP COLUMN original_order_id,
            DROP COLUMN signature_data,
            DROP COLUMN interaction_log_id,
            DROP COLUMN shipping_preference_id,
            DROP COLUMN return_status,
            DROP COLUMN credit_memo_id,
            DROP COLUMN delivery_instructions
        ");
    }
};
