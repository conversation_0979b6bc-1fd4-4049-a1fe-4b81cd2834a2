<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('item_colors', function (Blueprint $table) {
            $table->string('item_number')->unique()->nullable();
            $table->decimal('price', 10, 2);
            $table->string('photo')->nullable();
            $table->integer('stock')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('item_colors', function (Blueprint $table) {
            $table->dropColumn('item_number');
            $table->dropColumn('price');
            $table->dropColumn('photo');
            $table->dropColumn('stock');
        });
    }
};
