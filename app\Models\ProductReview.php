<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductReview extends Model
{
    protected $fillable=['user_id','product_id','rate','review','status', 'title', 'user_id', 'order_id', 'images', 'moderator_notes', 'deleted_at'];

    public function user_info(){
        return $this->hasOne('App\Models\User','id','user_id');
    }

    public static function getAllReview(){
        return ProductReview::with(['user_info', 'product', 'order'])->orderBy('created_at', 'DESC')->paginate(25);
    }
    public static function getAllUserReview(){
        return ProductReview::where('user_id',auth()->user()->id)->with('user_info')->paginate(10);
    }

    public function product(){
        return $this->hasOne(Product::class,'id','product_id');
    }

    public function order(){
        return $this->hasOne(Order::class,'id','order_id');
    }

    public function user(){
        return $this->hasOne(User::class,'id','user_id');
    }

}
