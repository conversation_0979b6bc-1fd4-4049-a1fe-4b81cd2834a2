<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE users MODIFY company_name VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_address VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_city VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_state VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_zip VARCHAR(10) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_address VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_city VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_state VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_zip VARCHAR(10) NULL');
        DB::statement('ALTER TABLE users MODIFY contact_phone VARCHAR(20) NULL');
        DB::statement('ALTER TABLE users MODIFY account_phone VARCHAR(20) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_name VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_business_name VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_tax_classification VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_street_address VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_city VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_state VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_zip_code VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY w9_tin VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_lat VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY billing_long VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_lat VARCHAR(255) NULL');
        DB::statement('ALTER TABLE users MODIFY shipping_long VARCHAR(255) NULL');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            //
        });
    }
};
