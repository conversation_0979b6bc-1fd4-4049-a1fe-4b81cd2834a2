<?php

namespace App\Listeners;

use App\Events\OrderConfirmationForCustomer;
use App\Mail\MailQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Mail;

class SendOrderConfirmationForCustomerEmail implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\OrderConfirmationForCustomer  $event
     * @return void
     */
    public function handle(OrderConfirmationForCustomer $event)
    {
        $order = $event->order;
        $showPrices = $event->showPrices;

        if (!$order->customer || !$order->customer->email) {
            return;
        }

        $data = [
            'order' => $order,
            'showPrices' => $showPrices,
        ];

        Mail::to($order->customer->email)->queue(new MailQueue(
            'backend.emails.order_confirmation_customer',
            $data,
            'Order Confirmation - Lamart Manufacturing'
        ));
    }
}
