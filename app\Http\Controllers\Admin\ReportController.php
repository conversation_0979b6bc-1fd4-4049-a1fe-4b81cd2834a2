<?php 
namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Order;

class ReportController extends Controller
{
    public function index(Request $request)
    {
        $salesmen = User::where('role', 'salesman')->get();
        $orders = [];

        if ($request->has(['from', 'to', 'salesman_id', 'customer_id'])) {
            $orders = Order::with(['user'])
                ->whereBetween('created_at', [$request->from, $request->to])
                ->where('salesman_id', $request->salesman_id)
                ->where('user_id', $request->customer_id)
                ->get();
        }

        return view('backend.report.orders', compact('salesmen', 'orders'));
    }

    public function getCustomers($salesman_id)
    {
        return response()->json(
            User::where('salesman_id', $salesman_id)->get() // assuming salesman is the 'created_by'
        );
    }
}