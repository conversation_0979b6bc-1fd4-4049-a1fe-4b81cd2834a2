<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\VisitReminder;
use Carbon\Carbon;
use App\Http\Controllers\EmailController;

class CronController extends Controller
{
    public function send_visit_reminders_salesman(EmailController $email_controller)
    {
        $today = Carbon::today();

        $recurring_reminders = VisitReminder::where('is_recurring', true)
            ->whereDate('visit_date', $today)
            ->with('customer')
            ->get();

        $reminders = VisitReminder::whereDate('visit_date', $today)
            ->with('customer')
            ->get();

        foreach ($recurring_reminders as $reminder) {
            $interval = $reminder->interval ?? 1;
            $frequency = $reminder->frequency ?? 'month';

            $next_visit_date = match ($frequency) {
                'day' => Carbon::parse($reminder->visit_date)->addDays($interval),
                'week' => Carbon::parse($reminder->visit_date)->addWeeks($interval),
                'month' => Carbon::parse($reminder->visit_date)->addMonths($interval),
                'year' => Carbon::parse($reminder->visit_date)->addYears($interval),
                default => Carbon::parse($reminder->visit_date)->addMonth(),
            };

            VisitReminder::create([
                'salesman_id' => $reminder->salesman_id,
                'customer_id' => $reminder->customer_id,
                'visit_date' => $next_visit_date,
                'note' => $reminder->note,
                'is_recurring' => true,
                'interval' => $interval,
                'frequency' => $frequency,
            ]);
        }

        foreach ($reminders as $reminder) {
            if ($reminder->customer && $reminder->customer->email) {
                $email_controller->send_visit_reminders_salesman($reminder);
            }
        }

        \Log::info('Visit reminders processed at ' . Carbon::now());
    }
}
