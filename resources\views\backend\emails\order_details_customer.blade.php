@extends('emails.layouts.master')

@section('title', 'Order Confirmation - Lamart Manufacturing')

@section('preheader', 'Your order #' . $order['order_number'] . ' has been confirmed')

@section('styles')
<style>
.product-entry {
   display: block;
   position: relative;
   padding: 15px 0;
   border-bottom: 1px solid #eee;
}
.product-entry .text {
   width: calc(100% - 115px);
   padding-left: 15px;
}
.product-entry .text h3 {
   margin-bottom: 5px;
   font-size: 16px;
}
.product-entry .text .price {
   color: #4D734E;
   font-weight: 600;
}
.order-summary {
   background: #f9f9f9;
   padding: 20px;
   margin: 20px 0;
}
.order-summary table {
   width: 100%;
}
.order-summary .total-line {
   border-top: 2px solid #4D734E;
   font-weight: 600;
   font-size: 16px;
}
</style>
@endsection

@section('content')
<!-- Order Confirmation Message -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 2em 0 0 0;">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text">
                  <h2 style="color: #4D734E;">Order Confirmation</h2>
                  <h4>
                     Hello {{$order['first_name']}} {{$order['last_name']}},<br><br>
                     Thank you for your order with Lamart Manufacturing. Your order has been successfully received and is being processed. You'll receive another email once your order ships.
                  </h4>
                  <p>
                     Order Number: <strong>{{$order['order_number']}}</strong><br>
                     Order Date: {{date('M d, Y', strtotime($order['created_at']))}} <br>
                     Track your order: <a href="https://lamartmfg.com/order/track?track=<?php echo base64_encode($order['order_number']); ?>" style="color: #4D734E;">Track Order</a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Order Items -->
<tr>
   <td valign="middle" class="hero bg_white order-summary" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 20px;">Order Items</h3>
                  
                  @if(isset($order['cart_info']) && is_array($order['cart_info']))
                     @foreach($order['cart_info'] as $item)
                     <div class="product-entry">
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                           <tr>
                              <td width="100" style="padding-right: 15px;">
                                 @if(isset($item['photo']))
                                    <img src="{{asset('storage/'.$item['photo'])}}" alt="{{$item['title']}}" width="80" style="border-radius: 4px;">
                                 @endif
                              </td>
                              <td>
                                 <h4 style="margin: 0 0 5px 0; font-size: 16px;">{{$item['title']}}</h4>
                                 <p style="margin: 0; color: #666; font-size: 14px;">
                                    Quantity: {{$item['quantity']}}<br>
                                    @if(isset($item['size']) && $item['size'])
                                       Size: {{$item['size']}}<br>
                                    @endif
                                    @if(isset($item['color']) && $item['color'])
                                       Color: {{$item['color']}}
                                    @endif
                                 </p>
                              </td>
                              <td style="text-align: right;">
                                 <span class="price" style="color: #4D734E; font-weight: 600; font-size: 16px;">
                                    ${{number_format($item['price'] * $item['quantity'], 2)}}
                                 </span>
                              </td>
                           </tr>
                        </table>
                     </div>
                     @endforeach
                  @endif

                  <!-- Order Summary -->
                  <div style="margin-top: 30px; padding: 20px; background: #f9f9f9; border-radius: 4px;">
                     <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                           <td style="padding: 5px 0;">Subtotal:</td>
                           <td style="text-align: right; padding: 5px 0;">${{number_format($order['sub_total'], 2)}}</td>
                        </tr>
                        @if($order['coupon'] > 0)
                        <tr>
                           <td style="padding: 5px 0;">Discount:</td>
                           <td style="text-align: right; padding: 5px 0; color: #4D734E;">-${{number_format($order['coupon'], 2)}}</td>
                        </tr>
                        @endif
                        <tr>
                           <td style="padding: 5px 0;">Shipping:</td>
                           <td style="text-align: right; padding: 5px 0;">${{number_format($order['delivery_charge'], 2)}}</td>
                        </tr>
                        <tr style="border-top: 2px solid #4D734E;">
                           <td style="padding: 10px 0; font-weight: 600; font-size: 18px;">Total:</td>
                           <td style="text-align: right; padding: 10px 0; font-weight: 600; font-size: 18px; color: #4D734E;">
                              ${{number_format($order['total_amount'], 2)}}
                           </td>
                        </tr>
                     </table>
                  </div>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Customer Details -->
<tr>
   <td valign="middle" class="hero bg_white order-summary" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <p style="font-weight: 500; font-size: 16px; margin-bottom: 10px;">Customer Details</p>
                  <p style="margin: 0;">{{$order['first_name']}} {{$order['last_name']}}</p>
                  <p style="margin: 0;">{{$order['email']}}</p>
                  <p style="margin: 0;">{{$order['phone']}}</p>
                  <p style="margin: 0;">{{$order['address1']}}{{$order['address2'] ? ', ' . $order['address2'] : ''}}, {{$order['post_code']}}, {{$order['country']}}</p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>
@endsection
