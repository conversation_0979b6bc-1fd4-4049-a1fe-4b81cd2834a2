<?php 
namespace App\Http\Controllers\Salesman;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Order;

class ReportController extends Controller
{
    public function index(Request $request)
    {
        $customers = User::whereIn('role', ['customer','user'])->where('salesman_id',Auth()->user()->id)->get();
        $orders = [];

        if ($request->has(['from', 'to', 'customer_id'])) {
            $orders = Order::with(['user'])
                ->whereBetween('created_at', [$request->from, $request->to])
                ->where('salesman_id', Auth()->user()->id)
                ->where('user_id', $request->customer_id)
                ->get();
        }

        return view('salesman.report.orders', compact('customers', 'orders'));
    }
}