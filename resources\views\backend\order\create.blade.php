@extends('backend.layouts.master')

@section('title', 'Create Order')

@section('main-content')
<div class="card">
  <h5 class="card-header">Create New Order</h5>
  <div class="card-body">
    <form method="post" action="{{route('order.store')}}" id="orderForm" enctype="multipart/form-data">
      @csrf
      <input type="hidden" name="first_name" id="first_name" value="{{ old('first_name', '') }}">
      <input type="hidden" name="last_name" id="last_name" value="{{ old('last_name', '') }}">
      <input type="hidden" name="address1" id="address1" value="{{ old('address1', '') }}">
      <input type="hidden" name="phone" id="phone" value="{{ old('phone', '') }}">
      <input type="hidden" name="email" id="email" value="{{ old('email', '') }}">
      <input type="hidden" name="post_code" id="post_code" value="{{ old('post_code', '') }}">

      <!-- Customer Selection -->
      <div class="row mb-4">
        <div class="col-md-12">
            <div class="form-group">
            <label for="customerSearch">Select Customer *</label>
            <select class="form-control select2" id="customerSearch" name="user_id" required>
                <option value="">-- Search customer --</option>
                @foreach($customers as $customer)
                <option value="{{$customer->id}}"
                    data-first_name="{{$customer->first_name}}"
                    data-last_name="{{$customer->last_name}}"
                    data-phone="{{$customer->contact_phone}}"
                    data-email="{{$customer->email}}"
                    data-address1="{{$customer->shipping_address}}"
                    data-post_code="{{$customer->shipping_zip}}">
                    {{$customer->first_name}} {{$customer->last_name}} ({{$customer->email}})
                </option>
                @endforeach
            </select>
            </div>
        </div>
      </div>

      <!-- Customer Details Preview -->
      <div class="row mb-4" id="customerDetails" style="display:none;">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6>Customer Information</h6>
            </div>
            <div class="card-body">
              <p><strong>Email:</strong> <span id="custEmail"></span></p>
              <p><strong>Phone:</strong> <span id="custPhone"></span></p>
              <p><strong>Address:</strong> <span id="custAddress"></span></p>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6>Preferences</h6>
            </div>
            <div class="card-body">
              <p><strong>Shipping:</strong> <span id="custShippingPref"></span></p>
              <p><strong>Payment:</strong> <span id="custPaymentPref"></span></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Order Type Toggle -->
      <div class="row mb-4">
        <div class="col-md-12">
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="order_type" id="regularOrder" value="regular" checked>
            <label class="form-check-label" for="regularOrder">Regular Order</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="radio" name="order_type" id="quickOrder" value="quick">
            <label class="form-check-label" for="quickOrder">Quick Order</label>
          </div>
          <div class="form-check form-check-inline">
            <input class="form-check-input" type="checkbox" name="save_as_draft" id="saveAsDraft" value="1">
            <label class="form-check-label" for="saveAsDraft">Save as Draft</label>
          </div>
        </div>
      </div>

      <!-- Quick Order Panel (hidden by default) -->
      <div class="row mb-4" id="quickOrderPanel" style="display:none;">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h6>Select from Previous Orders</h6>
              <button type="button" class="btn btn-sm btn-primary" id="addSelectedOrders">Add Selected to New Order</button>
            </div>
            <div class="card-body">
              <table class="table table-sm" id="previousOrdersTable">
                <thead>
                  <tr>
                    <th>Select</th>
                    <th>Order Date</th>
                    <th>Items</th>
                    <th>Total</th>
                  </tr>
                </thead>
                <tbody>
                  @if($previousOrders)
                    @foreach($previousOrders as $prevOrder)
                      <tr>
                        <td>
                          <input type="checkbox" name="previous_order_ids[]" value="{{$prevOrder->id}}">
                        </td>
                        <td>{{$prevOrder->created_at->format('M d, Y')}}</td>
                        <td>
                          <ul class="list-unstyled">
                            @foreach($prevOrder->items as $item)
                              <li>{{$item->quantity}} x {{$item->product->title}}</li>
                            @endforeach
                          </ul>
                        </td>
                        <td>${{number_format($prevOrder->total_amount, 2)}}</td>
                      </tr>
                    @endforeach
                  @endif
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Regular Order Panel -->
      <div class="row mb-4" id="regularOrderPanel">
        <div class="col-md-12">
          <div class="card">
            <div class="card-header">
              <h6>Order Items</h6>
            </div>
            <div class="card-body">
              <div class="row mb-3">
                <div class="col-md-8">
                  <select class="form-control select2" id="productSelect">
                    <option value="">-- Select product --</option>
                    @if($products)
                      @foreach($products as $product)
                        <option value="{{$product->id}}"
                          data-price="{{$product->price}}"
                          data-title="{{$product->title}}">
                          {{$product->title}} (${{number_format($product->price, 2)}})
                        </option>
                      @endforeach
                    @endif
                  </select>
                </div>
                <div class="col-md-2">
                  <input type="number" class="form-control" id="productQty" min="1" value="1">
                </div>
                <div class="col-md-2">
                  <button type="button" class="btn btn-primary btn-block" id="addProductBtn">Add</button>
                </div>
              </div>

              <table class="table table-sm" id="orderItemsTable">
                <thead>
                  <tr>
                    <th>Product</th>
                    <th>Price</th>
                    <th>Qty</th>
                    <th>Total</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <!-- Items will be added dynamically -->
                </tbody>
                <tfoot>
                  <tr>
                    <td colspan="3" class="text-right"><strong>Subtotal:</strong></td>
                    <td id="subtotalAmount">$0.00</td>
                    <td></td>
                  </tr>
                  <tr>
                    <td colspan="3" class="text-right"><strong>Shipping:</strong></td>
                    <td id="shippingAmount">$0.00</td>
                    <td></td>
                  </tr>
                  <tr>
                    <td colspan="3" class="text-right"><strong>Total:</strong></td>
                    <td id="totalAmount">$0.00</td>
                    <td></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Shipping & Payment -->
      <div class="row mb-4">
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6>Shipping Method</h6>
            </div>
            <div class="card-body">
              <div class="form-group">
                <select class="form-control" name="shipping_method" id="shippingMethod" required>
                  <option value="">-- Select shipping --</option>
                  <option value="ship">Ship</option>
                  <option value="delivery">Delivery</option>
                  <option value="pickup">Pickup</option>
                </select>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="save_shipping_pref" id="saveShippingPref" value="1">
                <label class="form-check-label" for="saveShippingPref">Save as customer preference</label>
              </div>
              <div class="form-group mt-3" id="shippingInstructionsContainer">
                <label for="delivery_instructions">Delivery Instructions</label>
                <textarea class="form-control" name="delivery_instructions" id="delivery_instructions" rows="2"></textarea>
              </div>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="card">
            <div class="card-header">
              <h6>Payment Method</h6>
            </div>
            <div class="card-body">
              <div class="form-group">
                <select class="form-control" name="payment_method" id="paymentMethod" required>
                  <option value="">-- Select payment --</option>
                  <option value="cod">Cash on Delivery</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="check">Check Transfer</option>
                </select>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="save_payment_pref" id="savePaymentPref" value="1">
                <label class="form-check-label" for="savePaymentPref">Save as customer preference</label>
              </div>

              <!-- Check Transfer Fields (hidden by default) -->
              <div id="checkTransferFields" style="display:none;" class="mt-3">
                <div class="form-group">
                  <label>Check Number</label>
                  <input type="text" class="form-control" name="check_number">
                </div>
                <div class="form-group">
                  <label>Check Front Image</label>
                  <input type="file" class="form-control" name="check_front" accept="image/*" capture="camera">
                  <small class="text-muted">Use your mobile camera to capture the front of the check</small>
                </div>
                <div class="form-group">
                  <label>Check Back Image</label>
                  <input type="file" class="form-control" name="check_back" accept="image/*" capture="camera">
                  <small class="text-muted">Use your mobile camera to capture the back of the check</small>
                </div>
                <div class="form-group">
                  <label>Check Date</label>
                  <input type="date" class="form-control" name="check_date">
                </div>
                <div class="form-group">
                  <label>Bank Name</label>
                  <input type="text" class="form-control" name="bank_name">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group mb-3">
        <button type="submit" class="btn btn-primary">Create Order</button>
        <a href="{{route('order.index')}}" class="btn btn-secondary">Cancel</a>
      </div>
    </form>
  </div>
</div>
@endsection

@push('styles')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
  .select2-container--default .select2-selection--single {
    height: 38px;
    border: 1px solid #ced4da;
  }
  #checkImagesPreview {
    display: flex;
    gap: 15px;
    margin-top: 10px;
  }
  .check-preview {
    border: 1px solid #ddd;
    padding: 5px;
    border-radius: 4px;
    position: relative;
  }
  .check-preview img {
    max-height: 150px;
    max-width: 100%;
  }
  .remove-check-img {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
  $(document).ready(function() {
    // Initialize Select2
    $('.select2').select2();

    // Customer selection change
    $('#customerSearch').change(function(){
      if($(this).val()) {
        $('#customerDetails').show();
        var selectedOption = $(this).find('option:selected');
        $('#custEmail').text(selectedOption.data('email'));
        $('#custPhone').text(selectedOption.data('phone'));
        $('#custAddress').text(selectedOption.data('address'));
        $('#custShippingPref').text(selectedOption.data('shipping-pref') || 'Not set');
        $('#custPaymentPref').text(selectedOption.data('payment-pref') || 'Not set');

        // Auto-set shipping/payment if preferences exist
        if(selectedOption.data('shipping-pref')) {
          $('#shippingMethod').val(selectedOption.data('shipping-pref')).trigger('change');
        }
        if(selectedOption.data('payment-pref')) {
          $('#paymentMethod').val(selectedOption.data('payment-pref')).trigger('change');
        }
      } else {
        $('#customerDetails').hide();
      }
    });

    // Order type toggle
    $('input[name="order_type"]').change(function(){
      if($(this).val() === 'quick') {
        $('#quickOrderPanel').show();
        $('#regularOrderPanel').hide();
      } else {
        $('#quickOrderPanel').hide();
        $('#regularOrderPanel').show();
      }
    });

    // Payment method change - show/hide check transfer fields
    $('#paymentMethod').change(function(){
      if($(this).val() === 'check') {
        $('#checkTransferFields').show();
      } else {
        $('#checkTransferFields').hide();
      }
    });

    // Add selected previous orders to new order
    $('#addSelectedOrders').click(function(){
      var selectedOrders = [];
      $('#previousOrdersTable input[type="checkbox"]:checked').each(function(){
        var orderId = $(this).val();
        var orderRow = $(this).closest('tr');
        var orderDate = orderRow.find('td').eq(1).text();
        var orderTotal = orderRow.find('td').eq(3).text();

        selectedOrders.push({
          id: orderId,
          date: orderDate,
          total: orderTotal
        });
      });

      if(selectedOrders.length === 0) {
        alert('Please select at least one previous order');
        return;
      }

      // Here you would typically make an AJAX call to get the order details
      // For demo purposes, we'll just show an alert
      alert('Adding ' + selectedOrders.length + ' previous orders to new order');

      // In a real implementation, you would:
      // 1. Make AJAX call to get order details for each selected order
      // 2. Add the items to the current order (or create a new combined order)
      // 3. Update the order totals
    });

    // Add product to order
    $('#addProductBtn').click(function(){
      var productId = $('#productSelect').val();
      var productTitle = $('#productSelect option:selected').data('title');
      var price = $('#productSelect option:selected').data('price');
      var qty = $('#productQty').val();

      if(!productId) {
        alert('Please select a product');
        return;
      }

      // Check if product already exists
      if($('#orderItemsTable tr[data-product="'+productId+'"]').length) {
        alert('This product is already in the order');
        return;
      }

      var total = (price * qty).toFixed(2);

      var newRow = `
        <tr data-product="${productId}">
          <td>${productTitle}</td>
          <td>$${price.toFixed(2)}</td>
          <td><input type="number" class="form-control qty-input" name="items[${productId}][quantity]" value="${qty}" min="1"></td>
          <td class="item-total">$${total}</td>
          <td><button type="button" class="btn btn-sm btn-danger remove-item">Remove</button></td>
          <input type="hidden" name="items[${productId}][price]" value="${price}">
          <input type="hidden" name="items[${productId}][product_id]" value="${productId}">
        </tr>
      `;

      $('#orderItemsTable tbody').append(newRow);
      updateTotals();
      $('#productSelect').val('').trigger('change');
      $('#productQty').val(1);
    });

    // Remove product from order
    $(document).on('click', '.remove-item', function(){
      $(this).closest('tr').remove();
      updateTotals();
    });

    // Update quantity
    $(document).on('change', '.qty-input', function(){
      var price = $(this).closest('tr').find('input[name*="[price]"]').val();
      var qty = $(this).val();
      var total = (price * qty).toFixed(2);
      $(this).closest('tr').find('.item-total').text('$'+total);
      updateTotals();
    });

    // Calculate order totals
    function updateTotals() {
      var subtotal = 0;
      $('.item-total').each(function(){
        subtotal += parseFloat($(this).text().replace('$',''));
      });

      // Simple shipping calculation (would be more complex in real app)
      var shipping = 0;
      if(subtotal > 0) {
        shipping = 5.00; // Flat rate for demo
      }

      var total = subtotal + shipping;

      $('#subtotalAmount').text('$'+subtotal.toFixed(2));
      $('#shippingAmount').text('$'+shipping.toFixed(2));
      $('#totalAmount').text('$'+total.toFixed(2));

      // Update hidden fields for form submission
      $('input[name="sub_total"]').val(subtotal.toFixed(2));
      $('input[name="shipping_charge"]').val(shipping.toFixed(2));
      $('input[name="total_amount"]').val(total.toFixed(2));
    }

    // Preview check images before upload
    $('input[name="check_front"], input[name="check_back"]').change(function(){
      var input = this;
      var previewId = $(this).attr('name') + '_preview';

      if (input.files && input.files[0]) {
        var reader = new FileReader();

        reader.onload = function(e) {
          // Create preview if it doesn't exist
          if($('#' + previewId).length === 0) {
            $('#checkImagesPreview').append(`
              <div class="check-preview">
                <span class="remove-check-img" data-for="${input.name}">×</span>
                <img id="${previewId}" src="${e.target.result}" alt="Check Image">
              </div>
            `);
          } else {
            $('#' + previewId).attr('src', e.target.result);
          }
        }

        reader.readAsDataURL(input.files[0]);
      }
    });

    // Remove check image preview
    $(document).on('click', '.remove-check-img', function(){
      var inputName = $(this).data('for');
      $('input[name="' + inputName + '"]').val('');
      $(this).parent().remove();
    });

    $('#customerSearch').on('change', function () {
        const selectedOption = $(this).find('option:selected');

        $('#first_name').val(selectedOption.data('first_name') || '');
        $('#last_name').val(selectedOption.data('last_name') || '');
        $('#address1').val(selectedOption.data('address1') || '');
        $('#phone').val(selectedOption.data('phone') || '');
        $('#email').val(selectedOption.data('email') || '');
        $('#post_code').val(selectedOption.data('post_code') || '');
    });

  });
</script>
@endpush
