<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('route_optimization_queue', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->unsignedBigInteger('route_id')->nullable();
            $table->decimal('address_lat', 10, 8);
            $table->decimal('address_lng', 11, 8);
            $table->string('address_text');
            $table->enum('status', ['pending', 'optimized', 'skipped'])->default('pending');
            $table->integer('sequence')->nullable()->comment('Order in the route');
            $table->timestamp('optimized_at')->nullable();
            $table->timestamps();

            $table->foreign('order_id')->references('id')->on('orders')->onDelete('cascade');
            $table->foreign('route_id')->references('id')->on('delivery_routes')->onDelete('set null');
        });

        DB::statement('CREATE INDEX idx_route_queue_optimization ON route_optimization_queue (route_id, status)');
        DB::statement('CREATE INDEX idx_route_queue_geo ON route_optimization_queue (address_lat, address_lng)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('route_optimization_queue');
    }
};
