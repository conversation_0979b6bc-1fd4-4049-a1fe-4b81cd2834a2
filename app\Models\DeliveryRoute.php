<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DeliveryRoute extends Model
{
    use HasFactory;

    protected $fillable = ['salesman_id', 'route_date', 'route_name', 'status', 'start_lat', 'start_lng', 'end_lat', 'end_lng', 'estimated_duration', 'estimated_distance', 'optimized_waypoints', 'original_waypoints'];

    protected $casts = [
        'optimized_waypoints' => 'array',
        'original_waypoints' => 'array',
        'route_date' => 'date'
    ];

    public function salesman()
    {
        return $this->belongsTo(User::class, 'salesman_id');
    }

    public function orders()
    {
        return $this->belongsToMany(Order::class, 'route_optimization_queue', 'route_id', 'order_id');
    }

    public function optimize()
    {
        $waypoints = $this->orders()
            ->where('status', '!=', 'delivered')
            ->get()
            ->map(function($order) {
                return [
                    'location' => "{$order->address_lat},{$order->address_lng}",
                    'order_id' => $order->id
                ];
            });

        // Call Google Maps API here and update optimized_waypoints
        // This is a simplified example
        $this->update([
            'optimized_waypoints' => $waypoints,
            'status' => 'optimized'
        ]);
    }
}
