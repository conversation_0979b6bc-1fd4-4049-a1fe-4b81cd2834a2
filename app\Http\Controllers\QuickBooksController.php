<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use QuickBooksOnline\API\DataService\DataService;
use QuickBooksOnline\API\Facades\Customer;

class QuickBooksController extends Controller
{
    protected function getDataService()
    {
        return DataService::Configure([
            'auth_mode'    => 'oauth2',
            'ClientID'     => env('QUICKBOOKS_CLIENT_ID'),
            'ClientSecret' => env('QUICKBOOKS_CLIENT_SECRET'),
            'RedirectURI'  => env('QUICKBOOKS_REDIRECT_URI'),
            'scope'        => "com.intuit.quickbooks.accounting",
            'baseUrl'      => "development" // Change to "production" for live mode
        ]);
    }

    public function authRedirect()
    {
        $dataService = $this->getDataService();
        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $authUrl = $OAuth2LoginHelper->getAuthorizationCodeURL();
        return redirect($authUrl);
    }

    public function callback(Request $request)
    {
        $dataService = $this->getDataService();
        $OAuth2LoginHelper = $dataService->getOAuth2LoginHelper();
        $accessToken = $OAuth2LoginHelper->exchangeAuthorizationCodeForToken($request->code, $request->realmId);
        
        session(['quickbooks_access_token' => $accessToken->getAccessToken()]);
        session(['quickbooks_realm_id' => $request->realmId]);

        return redirect()->route('qbo.auth');
    }

    public function getCustomers()
    {
        $dataService = $this->getDataService();
        $dataService->updateOAuth2Token(session('quickbooks_access_token'));
        $customers = $dataService->Query("SELECT * FROM Customer");
        
        return response()->json($customers);
    }
}

