@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Add Product</h5>
    <div class="card-body">
        <form method="post" action="{{ route('product.store') }}" enctype="multipart/form-data" id="productForm">
            @csrf

            <!-- Basic Information Section -->
            <div class="form-section">
                <h3>Basic Information</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="inputTitle" class="col-form-label">Title <span class="text-danger">*</span></label>
                            <input id="inputTitle" type="text" name="title" placeholder="Enter title" value="{{ old('title') }}" class="form-control" data-required="true" data-maxlength="255">
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="summary" class="col-form-label">Summary <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="summary" name="summary" data-required="true">{{ old('summary') }}</textarea>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="description" class="col-form-label">Description</label>
                            <textarea class="form-control" id="description" name="description">{{ old('description') }}</textarea>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing and Discount Section -->
            <div class="form-section">
                <h3>Pricing and Discount</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="discount" class="col-form-label">Discount (%)</label>
                            <input id="discount" type="number" step="any" name="discount" min="0" max="100" placeholder="Enter discount" value="{{ old('discount') }}" class="form-control" data-min="0" data-max="100">
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="price_level_lists">Price Level Lists</label>
                            <select id="openListModalDropdown" name="price_level_lists[]" class="form-control select2" multiple>
                                <option value="new"><strong>Create List</strong></option>
                                @foreach($price_level_lists as $price_level_list)
                                    <option value="{{ $price_level_list->id }}">{{ $price_level_list->title }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Inventory and Shipping Section -->
            <div class="form-section">
                <h3>Inventory and Shipping</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="items_per_box" class="col-form-label">Items per Box (Optional)</label>
                            <input id="items_per_box" type="number" name="items_per_box" placeholder="Enter items per box" value="{{ old('items_per_box') }}" class="form-control" data-min="0">
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="weight" class="col-form-label">Weight <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input id="weight" type="number" name="weight" placeholder="Enter weight" value="{{ old('weight') }}" class="form-control" step="0.01" min="0" max="999.99" data-required="true" data-min="0" data-max="999.99">
                                <select id="weight_unit" name="weight_unit" class="form-select" data-required="true" data-allowed="lbs,oz">
                                    <option value="lbs" {{ old('weight_unit', 'lbs') == 'lbs' ? 'selected' : '' }}>lbs</option>
                                    <option value="oz" {{ old('weight_unit', 'lbs') == 'oz' ? 'selected' : '' }}>oz</option>
                                </select>
                            </div>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="shipping_size" class="col-form-label">Shipping Size (W x D x H, in) <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-4">
                                    <input id="shipping_width" type="number" step="any" name="shipping_width" placeholder="Enter width" value="{{ old('shipping_width') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                                <div class="col-4">
                                    <input id="shipping_depth" type="number" step="any" name="shipping_depth" placeholder="Enter depth" value="{{ old('shipping_depth') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                                <div class="col-4">
                                    <input id="shipping_height" type="number" step="any" name="shipping_height" placeholder="Enter height" value="{{ old('shipping_height') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <span class="text-danger error-message shipping-size-error"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="actual_size" class="col-form-label">Actual Size (W x D x H x L x D, in) <span class="text-danger">*</span></label>
                            <div class="row">
                                <div class="col-4">
                                    <input id="actual_width" type="number" step="any" name="actual_width" placeholder="Enter width" value="{{ old('actual_width') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                                <div class="col-4">
                                    <input id="actual_depth" type="number" step="any" name="actual_depth" placeholder="Enter depth" value="{{ old('actual_depth') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                                <div class="col-4">
                                    <input id="actual_height" type="number" step="any" name="actual_height" placeholder="Enter height" value="{{ old('actual_height') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-4">
                                    <input id="actual_length" type="number" step="any" name="actual_length" placeholder="Enter length" value="{{ old('actual_length') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                                <div class="col-4">
                                    <input id="actual_diameter" type="number" step="any" name="actual_diameter" placeholder="Enter diameter" value="{{ old('actual_diameter') }}" class="form-control my-5px" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <span class="text-danger error-message actual-size-error"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories and Attributes Section -->
            <div class="form-section">
                <h3>Categories and Attributes</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="cat_id">Category <span class="text-danger">*</span></label>
                            <select name="cat_id[]" id="cat_id" class="form-control select2" multiple data-required="true" data-min="1">
                                <option value="new"><strong>Create Category</strong></option>
                                @foreach($categories as $key => $cat_data)
                                    <option value='{{ $cat_data->id }}'>{{ $cat_data->title }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="brand_id">Brand <span class="text-danger">*</span></label>
                            <select id="brand_id" name="brand_id" class="form-control select2" data-required="true">
                                <option value="">--Select Brand--</option>
                                <option value="new"><strong>Create Brand</strong></option>
                                @foreach($brands as $brand)
                                    <option value="{{ $brand->id }}">{{ $brand->title }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="color">Color <span class="text-danger">*</span></label>
                            <select name="color[]" id="color-select" class="form-control select2" multiple data-required="true" data-min="1">
                                <option value="new"><strong>Create New</strong></option>
                                @foreach($colors as $color)
                                    <option value="{{ $color->id }}">{{ $color->name }}</option>
                                @endforeach
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>

                    <div id="color-details-container" class="col-md-12 mt-3"></div>
                </div>
            </div>

            <!-- Status Section -->
            <div class="form-section">
                <h3>Status</h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status" class="col-form-label">Status <span class="text-danger">*</span></label>
                            <select name="status" class="form-control" data-required="true" data-allowed="active,inactive">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <span class="text-danger error-message"></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button type="submit" class="btn btn-success">Submit</button>
            </div>
        </form>
    </div>
</div>
@include('backend.modals.color')
@include('backend.modals.size')
@include('backend.modals.category')
@include('backend.modals.brand')
@include('backend.modals.list')
@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/summernote/summernote.min.css') }}">
<link rel="stylesheet" href="{{ asset('backend/css/select2.min.css') }}">
<style>
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .form-section h3 {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        color: #333;
    }
    .has-error .form-control, .has-error .form-select, .has-error .select2-container--default .select2-selection--multiple {
        border-color: #dc3545;
    }
    .error-message {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }
    .my-5px {
        margin-top: 5px;
        margin-bottom: 5px;
    }
</style>
@endpush

@push('scripts')
<script src="{{ asset('backend/summernote/summernote.min.js') }}"></script>
<script src="{{ asset('backend/js/select2.min.js') }}"></script>

<script>
$(document).ready(function() {
    $('#summary').summernote({
        placeholder: "Write short description.....",
        tabsize: 2,
        height: 100
    });
    $('#description').summernote({
        placeholder: "Write detail description.....",
        tabsize: 2,
        height: 150
    });

    $('.select2').select2();

    // Form validation
    $('#productForm').on('submit', function(e) {
        e.preventDefault();
        let isValid = true;
        let firstInvalidField = null;
        $('.error-message').empty();
        $('.form-group').removeClass('has-error');

        // Validation rules
        const fields = [
            { selector: '[name="title"]', rules: { required: true, maxlength: 255 } },
            { selector: '[name="summary"]', rules: { required: true } },
            { selector: '[name="description"]', rules: { maxlength: 65535 } },
            { selector: '[name="discount"]', rules: { min: 0, max: 100 } },
            { selector: '[name="items_per_box"]', rules: { min: 0 } },
            { selector: '[name="weight"]', rules: { required: true, min: 0, max: 999.99 } },
            { selector: '[name="weight_unit"]', rules: { required: true, allowed: ['lbs', 'oz'] } },
            { selector: '[name="shipping_width"]', rules: { min: 0 } },
            { selector: '[name="shipping_depth"]', rules: { min: 0 } },
            { selector: '[name="shipping_height"]', rules: { min: 0 } },
            { selector: '[name="actual_width"]', rules: { min: 0 } },
            { selector: '[name="actual_depth"]', rules: { min: 0 } },
            { selector: '[name="actual_height"]', rules: { min: 0 } },
            { selector: '[name="actual_length"]', rules: { min: 0 } },
            { selector: '[name="actual_diameter"]', rules: { min: 0 } },
            { selector: '[name="cat_id[]"]', rules: { required: true, min: 1 } },
            { selector: '[name="brand_id"]', rules: { required: true } },
            { selector: '[name="color[]"]', rules: { required: true, min: 1 } },
            { selector: '[name="status"]', rules: { required: true, allowed: ['active', 'inactive'] } },
        ];

        // Static field validation
        fields.forEach(field => {
            const $input = $(field.selector);
            const value = $input.val();
            const rules = field.rules;
            let error = '';

            // Required validation
            if (rules.required && (!value || value === '' || (Array.isArray(value) && value.length === 0))) {
                error = 'This field is required.';
            }

            // Max length validation
            if (rules.maxlength && value && value.length > rules.maxlength) {
                error = `Must not exceed ${rules.maxlength} characters.`;
            }

            // Min value validation
            if (rules.min !== undefined && value && parseFloat(value) < rules.min) {
                error = `Must be at least ${rules.min}.`;
            }

            // Max value validation
            if (rules.max !== undefined && value && parseFloat(value) > rules.max) {
                error = `Must not exceed ${rules.max}.`;
            }

            // Allowed values validation
            if (rules.allowed && value && !rules.allowed.includes(value)) {
                error = `Invalid value. Allowed: ${rules.allowed.join(', ')}.`;
            }

            // Min array validation
            if (rules.min && Array.isArray(value) && value.length < rules.min) {
                error = `Please select at least ${rules.min} option${rules.min > 1 ? 's' : ''}.`;
            }

            if (error) {
                isValid = false;
                $input.closest('.form-group').addClass('has-error');
                $input.siblings('.error-message').text(error);
                if (!firstInvalidField) {
                    firstInvalidField = $input;
                }
            }
        });

        // Shipping Size validation (at least one required)
        const shippingWidth = parseFloat($('[name="shipping_width"]').val()) || 0;
        const shippingDepth = parseFloat($('[name="shipping_depth"]').val()) || 0;
        const shippingHeight = parseFloat($('[name="shipping_height"]').val()) || 0;
        if (!shippingWidth && !shippingDepth && !shippingHeight) {
            isValid = false;
            $('.shipping-size-error').text('At least one shipping size field is required.');
            if (!firstInvalidField) {
                firstInvalidField = $('[name="shipping_width"]');
            }
        }

        // Actual Size validation (at least one required)
        const actualWidth = parseFloat($('[name="actual_width"]').val()) || 0;
        const actualDepth = parseFloat($('[name="actual_depth"]').val()) || 0;
        const actualHeight = parseFloat($('[name="actual_height"]').val()) || 0;
        const actualLength = parseFloat($('[name="actual_length"]').val()) || 0;
        const actualDiameter = parseFloat($('[name="actual_diameter"]').val()) || 0;
        if (!actualWidth && !actualDepth && !actualHeight && !actualLength && !actualDiameter) {
            isValid = false;
            $('.actual-size-error').text('At least one actual size field is required.');
            if (!firstInvalidField) {
                firstInvalidField = $('[name="actual_width"]');
            }
        }

        // Dynamic color field validation
        const selectedColors = $('#color-select').val() || [];
        selectedColors.forEach(colorId => {
            const fields = [
                { selector: `[name="color_item_number[${colorId}]"]`, rules: { required: true, maxlength: 255 }, label: 'Item Number' },
                { selector: `[name="color_price[${colorId}]"]`, rules: { required: true, min: 0 }, label: 'Price' },
                { selector: `[name="color_stock[${colorId}]"]`, rules: { required: true, min: 0 }, label: 'Quantity' },
                { selector: `[name="color_photo[${colorId}]"]`, rules: { required: true, file: { mimes: ['jpg', 'png', 'gif', 'jpeg'], max: 2048 } }, label: 'Main Photo' },
                { selector: `[name="color_additional_images[${colorId}][]"]`, rules: { file: { mimes: ['jpg', 'png', 'gif', 'jpeg'], max: 2048 } }, label: 'Additional Images' },
                { selector: `[name="color_barcode[${colorId}]"]`, rules: { maxlength: 255 }, label: 'Barcode' },
            ];

            fields.forEach(field => {
                const $input = $(field.selector);
                const value = $input.val();
                const files = $input[0].files;
                let error = '';

                // Required validation
                if (field.rules.required && (!value && (!files || files.length === 0))) {
                    error = `${field.label} is required.`;
                }

                // Max length validation
                if (field.rules.maxlength && value && value.length > field.rules.maxlength) {
                    error = `${field.label} must not exceed ${field.rules.maxlength} characters.`;
                }

                // Min value validation
                if (field.rules.min !== undefined && value && parseFloat(value) < field.rules.min) {
                    error = `${field.label} must be at least ${field.rules.min}.`;
                }

                // File validation
                if (field.rules.file && files && files.length > 0) {
                    Array.from(files).forEach(file => {
                        const fileType = file.type.split('/')[1].toLowerCase();
                        const fileSizeKB = file.size / 1024; // Size in KB
                        if (field.rules.file.mimes && !field.rules.file.mimes.includes(fileType)) {
                            error = `${field.label} must be a file of type: ${field.rules.file.mimes.join(', ')}.`;
                        }
                        if (field.rules.file.max && fileSizeKB > field.rules.file.max) {
                            error = `${field.label} must not exceed ${field.rules.file.max} KB.`;
                        }
                    });
                }

                if (error) {
                    isValid = false;
                    $input.closest('.form-group').addClass('has-error');
                    $input.siblings('.error-message').text(error);
                    if (!firstInvalidField) {
                        firstInvalidField = $input;
                    }
                }
            });
        });

        if (isValid) {
            this.submit();
        } else if (firstInvalidField) {
            $('html, body').animate({
                scrollTop: firstInvalidField.offset().top - 100 // Offset for better visibility
            }, 500);
        }
    });

    // Modal AJAX handlers
    $('#submitColorForm').click(function(e) {
        e.preventDefault();
        let form = $('#colorForm')[0];
        let formData = new FormData(form);
        $('.text-danger').text('');
        $.ajax({
            url: '{{ route("color.store") }}',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                $('#colorModal').modal('hide');
                $('#colorForm')[0].reset();
                let newOption = new Option(response.name, response.id, false, true);
                $('#color-select').append(newOption).trigger('change');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    if(errors.name) $('.error-name').text(errors.name[0]);
                    if(errors.color) $('.error-color').text(errors.color[0]);
                    if(errors.status) $('.error-status').text(errors.status[0]);
                } else {
                    alert('Something went wrong');
                }
            }
        });
    });

    $('#listForm').submit(function (e) {
        e.preventDefault();
        const form = $(this);
        const url = `{{ route('list.store') }}`;

        // Clear errors
        form.find('.text-danger').html('');

        $.ajax({
            url: url,
            method: "POST",
            data: form.serialize(),
            success: function (response) {
                $('#listModal').modal('hide');
                $('#listForm')[0].reset();
                let newOption = new Option(response.name, response.id, false, true);
                $('#openListModalDropdown').val(null).trigger('change');
                $('#openListModalDropdown').append(newOption).trigger('change');
            },
            error: function (xhr) {
                if (xhr.status === 422) {
                    const errors = xhr.responseJSON.errors;
                    for (let field in errors) {
                        form.find(`.error-${field}`).html(errors[field][0]);
                    }
                } else {
                    toastr.error("Something went wrong. Please try again.");
                }
            }
        });
    });

    $('#submitBrandForm').click(function(e) {
        e.preventDefault();
        let form = $('#brandForm')[0];
        let formData = new FormData(form);
        $('.text-danger').text('');
        $.ajax({
            url: '{{ route("brand.store") }}',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                $('#brandModal').modal('hide');
                $('#brandForm')[0].reset();
                let newOption = new Option(response.title, response.id, false, true);
                $('#brand_id').val(null).trigger('change');
                $('#brand_id').append(newOption).trigger('change');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    if (errors.title) $('.error-title').text(errors.title[0]);
                    if (errors.status) $('.error-status').text(errors.status[0]);
                } else {
                    alert('Something went wrong. Please try again.');
                }
            }
        });
    });

    $('#is_parent').change(function() {
        if ($(this).is(':checked')) {
            $('#parent_cat_div').addClass('d-none');
        } else {
            $('#parent_cat_div').removeClass('d-none');
        }
    });

    $('#submitCategoryForm').click(function(e) {
        e.preventDefault();
        let form = $('#categoryForm')[0];
        let formData = new FormData(form);
        $('.text-danger').text('');
        $.ajax({
            url: '{{ route("category.store") }}',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                $('#categoryModal').modal('hide');
                $('#categoryForm')[0].reset();
                $('#parent_cat_div').addClass('d-none');
                let newOption = new Option(response.title, response.id, false, true);
                $('#cat_id').val(null).trigger('change');
                $('#cat_id').append(newOption).trigger('change');
            },
            error: function(xhr) {
                if (xhr.status === 422) {
                    let errors = xhr.responseJSON.errors;
                    if (errors.title) $('.error-title').text(errors.title[0]);
                    if (errors.summary) $('.error-summary').text(errors.summary[0]);
                    if (errors.type) $('.error-type').text(errors.type[0]);
                    if (errors.photo) $('.error-photo').text(errors.photo[0]);
                    if (errors.status) $('.error-status').text(errors.status[0]);
                } else {
                    alert('An error occurred. Please try again.');
                }
            }
        });
    });

    $('#cat_id').change(function() {
        var cat_id = $(this).val();
        if (cat_id != null) {
            const selectedCats = $(this).val() || [];
            if(selectedCats[0] == 'new'){
                $('#categoryModal').modal('show');
                $('#cat_id').val(null).trigger('change');
                return true;
            }
            $.ajax({
                url: "/admin/category/" + cat_id + "/child",
                data: {
                    _token: "{{ csrf_token() }}",
                    id: cat_id
                },
                type: "POST",
                success: function(response) {
                    if (typeof(response) != 'object') {
                        response = $.parseJSON(response);
                    }
                    var html_option = "<option value=''>----Select sub category----</option>";
                    if (response.status) {
                        var data = response.data;
                        if (response.data) {
                            $('#child_cat_div').removeClass('d-none');
                            $.each(data, function(id, title) {
                                html_option += "<option value='" + id + "'>" + title + "</option>";
                            });
                        }
                    } else {
                        $('#child_cat_div').addClass('d-none');
                    }
                    $('#child_cat_id').html(html_option);
                }
            });
        }
    });

    $('#brand_id').on('change', function() {
        const selectedBrand = $(this).val() || [];
        if(selectedBrand == 'new'){
            $('#brandModal').modal('show');
            $('#brand_id').val(null).trigger('change');
        }
    });

    $('#color-select').on('change', function() {
        const selectedColors = $(this).val() || [];
        const container = $('#color-details-container');

        if(selectedColors[0] == 'new'){
            $('#colorModal').modal('show');
            $('#color-select').val(null).trigger('change');
            return;
        }
        container.empty();

        selectedColors.forEach(colorId => {
            const color = @json($colors->keyBy('id'));
            const colorName = color[colorId] ? color[colorId].name : 'Color ' + colorId;

            // Generate unique IDs for previews
            const barcodePreviewId = `barcode-preview-${colorId}`;
            const mainPhotoPreviewId = `main-photo-preview-${colorId}`;
            const additionalImagesPreviewId = `additional-images-preview-${colorId}`;

            container.append(`
                <div class="card mb-3 color-detail-card" data-color-id="${colorId}">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">${colorName}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Item Number <span class="text-danger">*</span></label>
                                    <input type="text" name="color_item_number[${colorId}]" placeholder="Enter item number"
                                        value="{{ old('color_item_number.${colorId}') }}" class="form-control" data-required="true" data-maxlength="255">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Price (USD) <span class="text-danger">*</span></label>
                                    <input type="number" step="any" name="color_price[${colorId}]" placeholder="Enter price"
                                        value="{{ old('color_price.${colorId}') }}" class="form-control" data-required="true" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Quantity <span class="text-danger">*</span></label>
                                    <input type="number" name="color_stock[${colorId}]" min="0" placeholder="Enter quantity"
                                        value="{{ old('color_stock.${colorId}') }}" class="form-control" data-required="true" data-min="0">
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Barcode</label>
                                    <input type="text" name="color_barcode[${colorId}]" class="form-control" data-maxlength="255">
                                    <div id="${barcodePreviewId}" style="margin-top:15px;max-height:100px;"></div>
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Main Photo <span class="text-danger">*</span></label>
                                    <input type="file" name="color_photo[${colorId}]" class="form-control-file" accept="image/*" data-required="true" data-file-mimes="jpg,png,gif" data-file-max="2048">
                                    <div id="${mainPhotoPreviewId}" style="margin-top:15px;max-height:100px;"></div>
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label">Additional Images (4-6)</label>
                                    <input type="file" name="color_additional_images[${colorId}][]" class="form-control-file" accept="image/*" multiple data-file-mimes="jpg,png,gif" data-file-max="2048">
                                    <div id="${additionalImagesPreviewId}" style="margin-top:15px;max-height:100px;"></div>
                                    <span class="text-danger error-message"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `);

            // Add image preview for file inputs
            $(`input[name="color_barcode[${colorId}]"]`).on('change', function() {
                const preview = $(`#${barcodePreviewId}`);
                preview.empty();
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.append(`<img src="${e.target.result}" style="height: 100px;">`);
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });

            $(`input[name="color_photo[${colorId}]"]`).on('change', function() {
                const preview = $(`#${mainPhotoPreviewId}`);
                preview.empty();
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        preview.append(`<img src="${e.target.result}" style="height: 100px;">`);
                    };
                    reader.readAsDataURL(this.files[0]);
                }
            });

            $(`input[name="color_additional_images[${colorId}][]"]`).on('change', function() {
                const preview = $(`#${additionalImagesPreviewId}`);
                preview.empty();
                if (this.files) {
                    Array.from(this.files).forEach(file => {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            preview.append(`<img src="${e.target.result}" style="height: 100px; margin-right: 10px;">`);
                        };
                        reader.readAsDataURL(file);
                    });
                }
            });
        });
    });

    $('#weight_unit').on('change', function() {
        let weight = parseFloat($('#weight').val()) || 0;
        if ($(this).val() === 'oz' && $('#weight').data('unit') === 'lbs') {
            $('#weight').val((weight * 16).toFixed(2)).data('unit', 'oz');
        } else if ($(this).val() === 'lbs' && $('#weight').data('unit') === 'oz') {
            $('#weight').val((weight / 16).toFixed(2)).data('unit', 'lbs');
        }
    });
});
</script>
@endpush
