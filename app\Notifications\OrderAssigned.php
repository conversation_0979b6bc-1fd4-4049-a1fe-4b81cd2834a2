<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Order;

class OrderAssigned extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    protected $order;
    public function __construct(Order $order){ $this->order = $order; }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database','mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Order Assigned to You')
            ->line("You have been assigned order {$this->order->order_number}.")
            ->action('Review Order', url("/salesman/orders/{$this->order->id}"));
    }

    public function toDatabase($notifiable)
    {
        return ['order_id'=>$this->order->id,'message'=>"Assigned order {$this->order->order_number}."];
    }
}
