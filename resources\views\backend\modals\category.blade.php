<!-- Category Modal -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Add New Category</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span>&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form id="categoryForm" method="post" enctype="multipart/form-data">
          @csrf
          <div class="form-group">
            <label>Title <span class="text-danger">*</span></label>
            <input type="text" name="title" class="form-control">
            <span class="text-danger error-title"></span>
          </div>

          <div class="form-group">
            <label>Summary</label>
            <textarea class="form-control" name="summary"></textarea>
            <span class="text-danger error-summary"></span>
          </div>

          <div class="form-group">
            <label>Type</label><br>
            <input type="radio" name="type" value="user"> User
            <input type="radio" name="type" value="item"> Item
            <span class="text-danger error-type"></span>
          </div>

          <div class="form-group">
            <label>Is Parent</label><br>
            <input type="checkbox" name="is_parent" id="is_parent" value="1" checked> Yes
          </div>

          <div class="form-group d-none" id="parent_cat_div">
            <label>Parent Category</label>
            <select name="parent_id" class="form-control">
              <option value="">--Select any category--</option>
              @foreach($parent_cats as $parent_cat)
                <option value="{{ $parent_cat->id }}">{{ $parent_cat->title }}</option>
              @endforeach
            </select>
          </div>

          <div class="form-group">
            <label>Photo</label>
            <div class="input-group">
              <span class="input-group-btn">
                <a id="lfm" data-input="thumbnail" data-preview="holder" class="btn btn-primary">
                  <i class="fa fa-picture-o"></i> Choose
                </a>
              </span>
              <input id="thumbnail" class="form-control" type="text" name="photo">
            </div>
            <div id="holder" style="margin-top:15px;max-height:100px;"></div>
            <span class="text-danger error-photo"></span>
          </div>

          <div class="form-group">
            <label>Status <span class="text-danger">*</span></label>
            <select name="status" class="form-control">
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
            <span class="text-danger error-status"></span>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-success" id="submitCategoryForm">Submit</button>
      </div>
    </div>
  </div>
</div>