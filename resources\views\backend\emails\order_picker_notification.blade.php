@extends('emails.layouts.master')

@section('title', 'New Order for Picking - Lamart Manufacturing')

@section('preheader', 'Order #' . $order['order_number'] . ' is ready for picking')

@section('content')
<!-- Picker Notification Message -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 2em 0 0 0;">
      <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text">
                  <h2 style="color: #4D734E;">New Order for Picking</h2>
                  <h4>
                     Hello,<br><br>
                     A new order is ready for picking and fulfillment. Please review the details below and prepare the items for shipment.
                  </h4>
                  <p>
                     Order Number: <strong>{{$order['order_number']}}</strong><br>
                     Order Date: {{date('M d, Y', strtotime($order['created_at']))}} <br>
                     Customer: {{$order['first_name']}} {{$order['last_name']}}<br>
                     Priority: <span style="color: #e74c3c; font-weight: bold;">
                        @if(isset($order['priority']))
                           {{ucfirst($order['priority'])}}
                        @else
                           Normal
                        @endif
                     </span>
                  </p>
                  <p style="margin-top: 20px;">
                     <a href="https://lamartmfg.com/admin/order/{{$order['id']}}" class="btn btn-primary" style="background: #4D734E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                        View Full Order
                     </a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Items to Pick -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Items to Pick</h3>
                  
                  @if(isset($order['cart_info']) && is_array($order['cart_info']))
                     @foreach($order['cart_info'] as $index => $item)
                     <div style="padding: 15px; margin: 10px 0; border: 1px solid #ddd; border-radius: 4px; background: #fafafa;">
                        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                           <tr>
                              <td width="60" style="padding-right: 15px; vertical-align: top;">
                                 <div style="background: #4D734E; color: white; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 18px;">
                                    {{$index + 1}}
                                 </div>
                              </td>
                              <td width="100" style="padding-right: 15px;">
                                 @if(isset($item['photo']))
                                    <img src="{{asset('storage/'.$item['photo'])}}" alt="{{$item['title']}}" width="80" style="border-radius: 4px; border: 1px solid #ddd;">
                                 @endif
                              </td>
                              <td>
                                 <h4 style="margin: 0 0 8px 0; color: #000; font-size: 16px;">{{$item['title']}}</h4>
                                 <p style="margin: 0; color: #666; line-height: 1.4;">
                                    <strong style="color: #e74c3c; font-size: 18px;">Quantity: {{$item['quantity']}}</strong><br>
                                    @if(isset($item['sku']) && $item['sku'])
                                       SKU: {{$item['sku']}}<br>
                                    @endif
                                    @if(isset($item['size']) && $item['size'])
                                       Size: {{$item['size']}}<br>
                                    @endif
                                    @if(isset($item['color']) && $item['color'])
                                       Color: {{$item['color']}}<br>
                                    @endif
                                    @if(isset($item['location']) && $item['location'])
                                       Location: <strong>{{$item['location']}}</strong>
                                    @endif
                                 </p>
                              </td>
                           </tr>
                        </table>
                     </div>
                     @endforeach
                  @endif
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Shipping Information -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Shipping Information</h3>
                  
                  <div style="background: #f9f9f9; padding: 20px; border-radius: 4px;">
                     <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                           <td width="50%" style="vertical-align: top; padding-right: 20px;">
                              <h4 style="margin: 0 0 10px 0; color: #000;">Ship To:</h4>
                              <p style="margin: 0; line-height: 1.6;">
                                 {{$order['first_name']}} {{$order['last_name']}}<br>
                                 {{$order['address1']}}<br>
                                 @if($order['address2'])
                                    {{$order['address2']}}<br>
                                 @endif
                                 {{$order['post_code']}}, {{$order['country']}}
                              </p>
                           </td>
                           <td width="50%" style="vertical-align: top;">
                              <h4 style="margin: 0 0 10px 0; color: #000;">Contact:</h4>
                              <p style="margin: 0; line-height: 1.6;">
                                 Email: {{$order['email']}}<br>
                                 Phone: {{$order['phone']}}
                              </p>
                              
                              @if(isset($order['shipping_method']))
                              <h4 style="margin: 15px 0 5px 0; color: #000;">Shipping Method:</h4>
                              <p style="margin: 0; font-weight: 500; color: #4D734E;">{{$order['shipping_method']}}</p>
                              @endif
                           </td>
                        </tr>
                     </table>
                  </div>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>

<!-- Special Instructions -->
<tr>
   <td valign="middle" class="hero bg_white" style="padding: 1.5rem 0;">
      <table class="bg_white" role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
         <tr>
            <td style="padding: 1.5rem; text-align: left;">
               <div class="text" style="color: #000000;">
                  <h3 style="color: #4D734E; margin-bottom: 15px;">Picking Instructions</h3>
                  
                  <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 15px 0;">
                     <ul style="margin: 0; padding-left: 20px; line-height: 1.8;">
                        <li>Verify all item quantities and specifications</li>
                        <li>Check for any damage before packing</li>
                        <li>Include packing slip with order details</li>
                        <li>Use appropriate packaging materials</li>
                        <li>Update order status once picked and packed</li>
                     </ul>
                  </div>

                  @if(isset($order['notes']) && $order['notes'])
                  <div style="background: #e8f4fd; border: 1px solid #bee5eb; padding: 15px; border-radius: 4px; margin: 15px 0;">
                     <h4 style="margin: 0 0 10px 0; color: #0c5460;">Special Notes:</h4>
                     <p style="margin: 0; color: #0c5460;">{{$order['notes']}}</p>
                  </div>
                  @endif

                  <p style="margin-top: 25px; text-align: center;">
                     <a href="https://lamartmfg.com/admin/order/{{$order['id']}}/pick" class="btn btn-primary" style="background: #4D734E; color: white; padding: 15px 30px; text-decoration: none; border-radius: 4px; display: inline-block; font-weight: 500;">
                        Mark as Picked
                     </a>
                  </p>
               </div>
            </td>
         </tr>
      </table>
   </td>
</tr>
@endsection
