<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use App\Models\Cart;
class Product extends Model
{
    protected $fillable=['title','slug','summary','description','cat_id','child_cat_id','brand_id','discount','status','size','stock','is_featured','condition','items_per_box','weight','shipping_width','shipping_height','shipping_length', 'weight_unit', 'actual_width', 'actual_height', 'actual_length', 'actual_diameter'];

    protected $appends = ['first_item_color_image', 'photo', 'price', 'all_photos'];

    protected $with = ['item_colors'];

    public function cat_info(){
        return $this->hasOne('App\Models\Category','id','cat_id');
    }
    public function sub_cat_info(){
        return $this->hasOne('App\Models\Category','id','child_cat_id');
    }
    public static function getAllProduct(){
        return Product::with(['cat_info','sub_cat_info'])->orderBy('id','desc')->paginate(10);
    }
    public function rel_prods(){
        return $this->hasMany('App\Models\Product','cat_id','cat_id')->with('item_colors')->where('status','active')->orderBy('id','DESC')->limit(8);
    }
    public function getReview(){
        return $this->hasMany('App\Models\ProductReview','product_id','id')->with('user_info')->where('status','active')->orderBy('id','DESC');
    }
    public static function getProductBySlug($slug){
        return Product::with(['cat_info','rel_prods','getReview','item_colors'])->where('slug',$slug)->first();
    }
    public static function countActiveProduct(){
        $data=Product::where('status','active')->count();
        if($data){
            return $data;
        }
        return 0;
    }

    public function carts(){
        return $this->hasMany(Cart::class)->whereNotNull('order_id');
    }

    public function wishlists(){
        return $this->hasMany(Wishlist::class)->whereNotNull('cart_id');
    }

    public function brand(){
        return $this->hasOne(Brand::class,'id','brand_id');
    }

    public function sizes()
    {
        return $this->belongsToMany(Size::class, 'item_sizes');
    }

    public function colors()
    {
        return $this->belongsToMany(Color::class, 'item_colors');
    }

    public function priceLevelLists()
    {
        return $this->belongsToMany(ItemPriceLevelList::class, 'item_price_level_lists')->withPivot('price');
    }

    public function categories()
    {
        return $this->belongsToMany(ItemCategory::class, 'item_categories');
    }

    public function item_colors()
    {
        return $this->hasMany(ItemColor::class);
    }

    public function getFirstItemColorImageAttribute()
    {
        return $this->item_colors->first()->photo ?? null;
    }

    public function getTotalStockAttribute() {
        return $this->item_colors->sum('stock');
    }

    // Helper methods for backward compatibility with frontend
    public function getPhotoAttribute()
    {
        $firstItemColor = $this->item_colors->first();
        return $firstItemColor ? $firstItemColor->photo : null;
    }

    public function getPriceAttribute()
    {
        $firstItemColor = $this->item_colors->first();
        return $firstItemColor ? $firstItemColor->price : 0;
    }

    public function getAllPhotosAttribute()
    {
        return $this->item_colors->pluck('photo')->filter()->implode(',');
    }

}
