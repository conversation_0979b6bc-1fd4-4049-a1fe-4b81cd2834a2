<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_drafts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('Customer ID');
            $table->unsignedBigInteger('salesman_id')->comment('Salesman ID');
            $table->json('cart_items')->comment('Serialized cart data');
            $table->string('draft_name')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->enum('status', ['active', 'converted', 'expired'])->default('active');
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('salesman_id')->references('id')->on('users')->onDelete('cascade');
        });

        DB::statement('CREATE INDEX idx_drafts_salesman_customer ON order_drafts (salesman_id, user_id)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_drafts');
    }
};
