<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderDraft extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'salesman_id', 'cart_items', 'draft_name', 'expires_at', 'status'
    ];

    protected $casts = [
        'cart_items' => 'array',
        'expires_at' => 'datetime'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function salesman()
    {
        return $this->belongsTo(User::class, 'salesman_id');
    }

    public function convertToOrder()
    {
        $orderData = [
            'user_id' => $this->user_id,
            'salesman_id' => $this->salesman_id,
            'order_number' => 'ORD-'.strtoupper(uniqid()),
            'first_name' => $this->user->first_name,
            'last_name' => $this->user->last_name,
            'email' => $this->user->email,
            'phone' => $this->user->phone,
            'address1' => $this->user->address1,
            'address2' => $this->user->address2,
            'country' => $this->user->country,
            'post_code' => $this->user->post_code,
            'status' => 'new',
            'is_draft' => false,
        ];

        $order = Order::create($orderData);

        foreach ($this->cart_items as $item) {
            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $item['product_id'],
                'price' => $item['price'],
                'quantity' => $item['quantity'],
                'total' => $item['price'] * $item['quantity'],
            ]);
        }

        $this->update(['status' => 'converted']);

        return $order;
    }
}
