<?php

namespace App\Http\Controllers;
use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use Yajra\DataTables\Facades\DataTables;
use App\Http\Controllers\EmailController;
use App\Models\Category;
use App\Models\PriceLevelList;
use App\Models\UserPriceLevelList;
use App\Models\UserCategories;

class UsersController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(Auth()->user()->role == 'salesman'){
            $users = User::whereIn('role', ['user','customer'])->where('salesman_id',Auth()->user()->id)->paginate(25);
            return view('salesman.customers.index', compact('users'));
        } else {
            $users=User::orderBy('id','ASC')->paginate(25);
            return view('backend.users.index')->with('users',$users);
        }
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $price_level_lists = PriceLevelList::all();
        $categories = Category::where('type', 'user')->get();
        if(Auth()->user()->role == 'salesman'){
            return view('salesman.customers.create', compact( 'price_level_lists', 'categories'));
        } else {
            $userRoles = ['admin', 'user', 'customer', 'salesman', 'office', 'picker'];
            $salesmans = User::where('role', 'salesman')->where('status','active')->get(['id', 'first_name', 'last_name']);
            return view('backend.users.create', compact('userRoles', 'price_level_lists', 'categories','salesmans'));
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, EmailController $email_controller)
    {
        $allowedRoles = ['admin', 'user', 'customer', 'salesman', 'office', 'picker'];
        $rolesToSkipDetails = ['admin', 'office', 'salesman'];
        $isUserDetailsRequired = !in_array($request->role, $rolesToSkipDetails);

        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'status' => 'required|in:active,inactive',
            'role' => Auth()->user()->role == 'admin' ? 'required|in:' . implode(',', $allowedRoles) : 'nullable',
            'cat_id.*' => 'exists:categories,id',
            'price_level_lists' => 'nullable|array|min:1',
            'price_level_lists.*' => 'exists:price_level_lists,id',
            'w9_name' => 'nullable|string|max:255',
            'w9_business_name' => 'nullable|string|max:255',
            'w9_taxClassification' => 'nullable|string|max:255',
            'w9_streetAddress' => 'nullable|string|max:255',
            'w9_city' => 'nullable|string|max:255',
            'w9_state' => 'nullable|string|max:255',
            'w9_zipCode' => 'nullable|string|max:255',
            'w9_tin' => 'nullable|string|max:255',
            'salesman_id' => Auth()->user()->role == 'admin' && in_array($request->role, ['customer', 'user']) ? 'required|exists:users,id' : 'nullable',
            'from_order_create' => 'nullable|string',
        ];

        if ($isUserDetailsRequired) {
            $rules['company_name'] = 'required|string|max:255';
            $rules['billing_address'] = 'required|string|max:255';
            $rules['billing_city'] = 'required|string|max:255';
            $rules['billing_state'] = 'required|string|max:255';
            $rules['billing_zip'] = 'required|string|max:10';
            $rules['shipping_address'] = 'required|string|max:255';
            $rules['shipping_city'] = 'required|string|max:255';
            $rules['shipping_state'] = 'required|string|max:255';
            $rules['shipping_zip'] = 'required|string|max:10';
            $rules['contact_phone'] = 'required|string|max:20';
            $rules['account_phone'] = 'nullable|string|max:20';
            $rules['price_level_lists'] = 'required|array|min:1';
            $rules['cat_id'] = 'required|array|min:1';
        } else {
            $rules['company_name'] = 'nullable|string|max:255';
            $rules['billing_address'] = 'nullable|string|max:255';
            $rules['billing_city'] = 'nullable|string|max:255';
            $rules['billing_state'] = 'nullable|string|max:255';
            $rules['billing_zip'] = 'nullable|string|max:10';
            $rules['shipping_address'] = 'nullable|string|max:255';
            $rules['shipping_city'] = 'nullable|string|max:255';
            $rules['shipping_state'] = 'nullable|string|max:255';
            $rules['shipping_zip'] = 'nullable|string|max:10';
            $rules['contact_phone'] = 'nullable|string|max:20';
            $rules['account_phone'] = 'nullable|string|max:20';
        }

        $request->validate($rules);

        $data = $request->all();
        $data['w9_name'] = $request->w9_name;
        $data['w9_business_name'] = $request->w9_business_name;
        $data['w9_tax_classification'] = $request->w9_taxClassification;
        $data['w9_street_address'] = $request->w9_streetAddress;
        $data['w9_city'] = $request->w9_city;
        $data['w9_state'] = $request->w9_state;
        $data['w9_zip_code'] = $request->w9_zipCode;
        $data['w9_tin'] = $request->w9_tin;
        $data['password'] = Hash::make($data['password']);
        if (Auth()->user()->role == 'salesman') {
            $data['salesman_id'] = $request->salesman_id = Auth()->user()->id;
            $data['role'] = $request->role = 'customer';
            $data['status'] = 'inactive';
        }
        unset($data['same_as_billing']);

        try {
            DB::beginTransaction();
            $user = User::create($data);

            if ($request->price_level_lists) {
                foreach ($request->price_level_lists as $price_level) {
                    UserPriceLevelList::create([
                        'user_id' => $user->id,
                        'price_level_list_id' => $price_level,
                    ]);
                }
            }

            if ($request->cat_id) {
                foreach ($request->cat_id as $cat) {
                    UserCategories::create([
                        'user_id' => $user->id,
                        'category_id' => $cat,
                    ]);
                }
            }

            if ($user) {
                $email_controller->sendAdminNewRegistrationEmail($data);
                request()->session()->flash('success', 'Successfully added user');
                DB::commit();
            } else {
                request()->session()->flash('error', 'Error occurred while adding user');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            request()->session()->flash('error', 'Error occurred while adding user');
        }

        if ($request->from_order_create == 'true') {
            return response()->json(['user_id' => $user->id]);
        }

        return redirect()->route('users.index');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user=User::findOrFail($id);
        $price_level_lists = PriceLevelList::all();
        $user_price_level_lists = UserPriceLevelList::where('user_id', $id)->pluck('price_level_list_id')->toArray();

        $categories = Category::where('type', 'user')->get();
        $user_categories = UserCategories::where('user_id', $id)->pluck('category_id')->toArray();
        if(Auth()->user()->role == 'salesman'){
            return view('salesman.customers.edit', compact( 'user','user_price_level_lists','price_level_lists', 'categories','user_categories'));
        } else {
            $userRoles = ['admin', 'user', 'customer', 'salesman', 'office', 'picker'];
            $salesmans = User::where('role', 'salesman')->where('status','active')->get(['id', 'first_name', 'last_name']);
            return view('backend.users.edit', compact('user', 'userRoles', 'price_level_lists', 'user_price_level_lists', 'categories', 'user_categories','salesmans'));
        }
        // dd($user_price_level_lists, $user_categories);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $allowedRoles = ['admin', 'user', 'customer', 'salesman', 'office', 'picker'];
        $rolesToSkipDetails = ['admin', 'office', 'salesman'];
        $isUserDetailsRequired = !in_array($request->role, $rolesToSkipDetails);

        $rules = [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'status' => 'required|in:active,inactive',
            'role' => Auth()->user()->role == 'admin' ? 'required|in:' . implode(',', $allowedRoles) : 'nullable',
            'cat_id' => 'nullable|array|min:1',
            'cat_id.*' => 'exists:categories,id',
            'price_level_lists' => 'nullable|array|min:1',
            'price_level_lists.*' => 'exists:price_level_lists,id',
            'w9_name' => 'nullable|string|max:255',
            'w9_business_name' => 'nullable|string|max:255',
            'w9_taxClassification' => 'nullable|string|max:255',
            'w9_streetAddress' => 'nullable|string|max:255',
            'w9_city' => 'nullable|string|max:255',
            'w9_state' => 'nullable|string|max:255',
            'w9_zipCode' => 'nullable|string|max:255',
            'w9_tin' => 'nullable|string|max:255',
            'salesman_id' => Auth()->user()->role == 'admin' && in_array($request->role, ['customer', 'user']) ? 'required|exists:users,id' : 'nullable',
        ];

        if ($isUserDetailsRequired) {
            $rules['company_name'] = 'required|string|max:255';
            $rules['billing_address'] = 'required|string|max:255';
            $rules['billing_city'] = 'required|string|max:255';
            $rules['billing_state'] = 'required|string|max:255';
            $rules['billing_zip'] = 'required|string|max:10';
            $rules['shipping_address'] = 'required|string|max:255';
            $rules['shipping_city'] = 'required|string|max:255';
            $rules['shipping_state'] = 'required|string|max:255';
            $rules['shipping_zip'] = 'required|string|max:10';
            $rules['contact_phone'] = 'required|string|max:20';
            $rules['account_phone'] = 'nullable|string|max:20';
            $rules['price_level_lists'] = 'required|array|min:1';
            $rules['cat_id'] = 'required|array|min:1';
        } else {
            $rules['company_name'] = 'nullable|string|max:255';
            $rules['billing_address'] = 'nullable|string|max:255';
            $rules['billing_city'] = 'nullable|string|max:255';
            $rules['billing_state'] = 'nullable|string|max:255';
            $rules['billing_zip'] = 'nullable|string|max:10';
            $rules['shipping_address'] = 'nullable|string|max:255';
            $rules['shipping_city'] = 'nullable|string|max:255';
            $rules['shipping_state'] = 'nullable|string|max:255';
            $rules['shipping_zip'] = 'nullable|string|max:10';
            $rules['contact_phone'] = 'nullable|string|max:20';
            $rules['account_phone'] = 'nullable|string|max:20';
        }

        $request->validate($rules);

        try {
            DB::beginTransaction();
            $data = $request->all();
            $user = User::findOrFail($id);
            $status = $user->status;

            if ($request->price_level_lists) {
                UserPriceLevelList::where('user_id', $user->id)->delete();
                foreach ($request->price_level_lists as $price_level) {
                    UserPriceLevelList::create([
                        'user_id' => $user->id,
                        'price_level_list_id' => $price_level,
                    ]);
                }
            }

            if ($request->cat_id) {
                UserCategories::where('user_id', $user->id)->delete();
                foreach ($request->cat_id as $cat) {
                    UserCategories::create([
                        'user_id' => $user->id,
                        'category_id' => $cat,
                    ]);
                }
            }

            $data['w9_name'] = $request->w9_name;
            $data['w9_business_name'] = $request->w9_business_name;
            $data['w9_tax_classification'] = $request->w9_taxClassification;
            $data['w9_street_address'] = $request->w9_streetAddress;
            $data['w9_city'] = $request->w9_city;
            $data['w9_state'] = $request->w9_state;
            $data['w9_zip_code'] = $request->w9_zipCode;
            $data['w9_tin'] = $request->w9_tin;

            if (Auth()->user()->role == 'salesman') {
                $data['salesman_id'] = $request->salesman_id = Auth()->user()->id;
                $data['role'] = $request->role = 'customer';
                $data['status'] = $request->status;
            }

            $user->update($data);

            if ($user) {
                if($data['status'] != $status){
                    $email_controller = new EmailController();
                    $email_controller->userStatusChanged($data);
                }
                request()->session()->flash('success', 'Successfully updated');
                DB::commit();
            } else {
                request()->session()->flash('error', 'Error occurred while updating');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            request()->session()->flash('error', 'Error occurred while updating user');
        }

        if(Auth()->user()->role == 'salesman'){
            return redirect()->route('salesman.users.index');
        }

        return redirect()->route('users.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $delete=User::findorFail($id);
        $status=$delete->delete();
        if($status){
            request()->session()->flash('success','User Successfully deleted');
        }
        else{
            request()->session()->flash('error','There is an error while deleting users');
        }
        return redirect()->route('users.index');
    }

    public  function map()
    {
        if(Auth()->user()->role == 'salesman'){
            $users = User::whereIn('role', ['customer','user'])->where('salesman_id', Auth()->user()->id)->whereNotNull('shipping_lat')->whereNotNull('shipping_long')->where('status','active')->get();
        } else {
            $users = User::whereIn('role', ['customer','user'])->whereNotNull('shipping_lat')->whereNotNull('shipping_long')->where('status','active')->get();
        }
        return view('backend.users.map',compact('users'));
    }

    public function navigate(User $user)
    {
        return view('salesman.customers.navigate', compact('user'));
    }

    public function checkEmail(Request $request)
    {
        $exists = User::where('email', $request->email)->exists();
        return response()->json(['exists' => $exists]);
    }
}
