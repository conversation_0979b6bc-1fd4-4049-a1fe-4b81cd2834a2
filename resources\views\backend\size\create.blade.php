@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Add Size</h5>
    <div class="card-body">
        <form method="post" action="{{ route('size.store') }}" enctype="multipart/form-data">
            @csrf

            <div class="form-group">
                <label for="name" class="col-form-label">Size Name <span class="text-danger">*</span></label>
                <input id="name" type="text" name="name" placeholder="Enter name"  value="{{old('name')}}" class="form-control">
                @error('name')
                    <span class="text-danger">{{$message}}</span>
                @enderror
            </div>

            <div class="form-group">
                <label for="status" class="col-form-label">Status <span class="text-danger">*</span></label>
                <select name="status" class="form-control">
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
                @error('status')
                    <span class="text-danger">{{$message}}</span>
                @enderror
            </div>

            <!-- Submit Button -->
            <div class="form-group mb-3">
                <button type="reset" class="btn btn-warning">Reset</button>
                <button type="submit" class="btn btn-success">Submit</button>
            </div>
        </form>
    </div>
</div>

@endsection

@push('styles')
<link rel="stylesheet" href="{{ asset('backend/summernote/summernote.min.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" />
@endpush

@push('scripts')
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>
<script src="{{ asset('backend/summernote/summernote.min.js') }}"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>

<script>
    $('#lfm').filemanager('image');
</script>
@endpush
