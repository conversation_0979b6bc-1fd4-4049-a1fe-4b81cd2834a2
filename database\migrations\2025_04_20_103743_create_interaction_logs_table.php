<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('interaction_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->comment('Customer ID');
            $table->unsignedBigInteger('salesman_id')->comment('Salesman ID');
            $table->unsignedBigInteger('order_id')->nullable();
            $table->enum('interaction_type', [
                'visit',
                'call',
                'email',
                'order_created',
                'order_updated',
                'delivery_scheduled',
                'payment_reminder',
                'support_request'
            ]);
            $table->text('notes')->nullable();
            $table->decimal('location_lat', 10, 8)->nullable();
            $table->decimal('location_lng', 11, 8)->nullable();
            $table->string('duration')->nullable()->comment('HH:MM:SS format');
            $table->timestamp('scheduled_followup')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('salesman_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('set null');
        });

        DB::statement('CREATE INDEX idx_interaction_user_salesman ON interaction_logs (user_id, salesman_id)');
        DB::statement('CREATE INDEX idx_interaction_dates ON interaction_logs (created_at, scheduled_followup)');
        DB::statement('CREATE INDEX idx_interaction_geo ON interaction_logs (location_lat, location_lng)');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('interaction_logs');
    }
};
