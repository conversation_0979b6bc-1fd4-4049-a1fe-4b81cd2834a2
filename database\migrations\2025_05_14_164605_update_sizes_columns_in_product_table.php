<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement('ALTER TABLE products RENAME COLUMN width TO shipping_width');
        DB::statement('ALTER TABLE products RENAME COLUMN height TO shipping_height');
        DB::statement('ALTER TABLE products RENAME COLUMN length TO shipping_depth');

        Schema::table('products', function (Blueprint $table) {
            $table->decimal('actual_width', 8, 2)->nullable();
            $table->decimal('actual_depth', 8, 2)->nullable();
            $table->decimal('actual_height', 8, 2)->nullable();
            $table->decimal('actual_length', 8, 2)->nullable();
            $table->decimal('actual_diameter', 8, 2)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement('ALTER TABLE products RENAME COLUMN shipping_width TO width');
        DB::statement('ALTER TABLE products RENAME COLUMN shipping_height TO height');
        DB::statement('ALTER TABLE products RENAME COLUMN shipping_depth TO length');

        Schema::table('products', function (Blueprint $table) {
            $table->dropColumn([
                'actual_width',
                'actual_depth',
                'actual_height',
                'actual_length',
                'actual_diameter'
            ]);
        });
    }
};
