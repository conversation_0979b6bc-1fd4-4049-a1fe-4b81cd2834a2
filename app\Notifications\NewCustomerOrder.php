<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use App\Models\Order;

class NewCustomerOrder extends Notification
{
    use Queueable;

    /**
          * Create a new notification instance.
     */
    protected $order;
    public function __construct(Order $order){ $this->order = $order; }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database','mail'];
    }

    

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Order Placed')
            ->line("Order {$this->order->order_number} was placed by {$this->order->first_name}.")
            ->action('View Order', url("/salesman/orders/{$this->order->id}"));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toDatabase($notifiable)
    {
        return [
            'order_id'=>$this->order->id,
            'message'=>"New order {$this->order->order_number} placed.",
        ];
    }
}
