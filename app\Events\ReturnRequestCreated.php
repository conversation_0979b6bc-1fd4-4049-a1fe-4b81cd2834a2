<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReturnRequestCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $order;
    public $returns;
    public $reason;

    /**
     * Create a new event instance.
     *
     * @param Order $order
     * @param array $returns
     * @param string $reason
     * @return void
     */
    public function __construct(Order $order, $returns, $reason)
    {
        $this->order = $order;
        $this->returns = $returns;
        $this->reason = $reason;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('channel-name');
    }
}
