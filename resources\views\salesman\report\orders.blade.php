@extends('backend.layouts.master')

@section('main-content')

<div class="card">
    <h5 class="card-header">Order Report</h5>
    <div class="card-body">
        <form method="GET" action="{{ route('salesman.report.index') }}">
            <div class="row">
                <div class="col-md-3">
                    <label>From Date</label>
                    <input type="date" name="from" value="{{ request('from') }}" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label>To Date</label>
                    <input type="date" name="to" value="{{ request('to') }}" class="form-control" required>
                </div>
                <div class="col-md-3">
                    <label>Customer</label>
                    <select name="customer_id" id="customer" class="form-control select2" required>
                        <option value="">Select Customer</option>
                            @foreach($customers as $customer)
                                <option value="{{ $customer->id }}" {{ request('customer_id') == $customer->id ? 'selected' : '' }}>
                                    {{ $customer->first_name }} {{ $customer->last_name }}
                                </option>
                            @endforeach
                    </select>
                </div>
            </div>

            <div class="form-group mt-3">
                <button type="submit" class="btn btn-success">Filter</button>
            </div>
        </form>
    </div>
</div>

@if(!empty($orders))
<div class="card mt-4">
    <h5 class="card-header">Filtered Orders</h5>
    <div class="card-body table-responsive">
        <table class="table table-bordered" id="reportTable">
            <thead>
                <tr>
                    <th>#ID</th>
                    <th>Customer</th>
                    <th>Date</th>
                    <th>Total</th>
                    <th>Amnt. Paid</th>
                    <th>Amnt. Left</th>
                </tr>
            </thead>
            <tbody>
                @foreach($orders as $order)
                <tr>
                    <td>{{ $order->id }}</td>
                    <td>{{ $order?->user?->first_name.' '.$order?->user?->last_name ?? '-' }}</td>
                    <td>{{ $order->created_at->format('Y-m-d') }}</td>
                    <td>{{ $order->total_amount ?? '-' }}</td>
                    <td>{{ $order->amount_paid ?? '-' }}</td>
                    <td>{{ $order->amount_left ?? '-' }}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <th></th>
                    <th></th>
                    <th style="text-align:right">Total:</th>
                    <th id="total-footer"></th>
                    <th></th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
@endif

@endsection

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" /><link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/jquery.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.3.6/css/buttons.dataTables.min.css">
@endpush

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.flash.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();

        $('#salesman').on('change', function () {
            let salesmanId = $(this).val();
            if (!salesmanId) return;

            $('#customer').html('<option value="">Loading...</option>');
            $.get(`../report/customers/${salesmanId}`, function (data) {
                let options = `<option value="">Select Customer</option>`;
                data.forEach(function (customer) {
                    options += `<option value="${customer.id}">${customer.first_name} ${customer.last_name} | ${customer.email} </option>`;
                });
                $('#customer').html(options);
            });
        });

        $('#reportTable').DataTable({
        dom: 'Bfrtip',
        buttons: ['copy', 'csv', 'excel', 'pdf', 'print'],
        footerCallback: function ( row, data, start, end, display ) {
            var api = this.api();

            // Helper to parse float
            var parseValue = function (i) {
                return typeof i === 'string' ?
                    parseFloat(i.replace(/[\$,]/g, '')) || 0 :
                    typeof i === 'number' ? i : 0;
            };

            // Indices of the columns
            var totalAmountCol = 3;
            var amountPaidCol = 4;
            var amountLeftCol = 5;

            // Calculate column totals
            var totalAmount = api.column(totalAmountCol, { search: 'applied' }).data().reduce(function (a, b) {
                return parseValue(a) + parseValue(b);
            }, 0);

            var amountPaid = api.column(amountPaidCol, { search: 'applied' }).data().reduce(function (a, b) {
                return parseValue(a) + parseValue(b);
            }, 0);

            var amountLeft = api.column(amountLeftCol, { search: 'applied' }).data().reduce(function (a, b) {
                return parseValue(a) + parseValue(b);
            }, 0);

            // Update footer cells
            $(api.column(totalAmountCol).footer()).html(totalAmount.toFixed(2));
            $(api.column(amountPaidCol).footer()).html(amountPaid.toFixed(2));
            $(api.column(amountLeftCol).footer()).html(amountLeft.toFixed(2));
        }
    });
    });
</script>
@endpush